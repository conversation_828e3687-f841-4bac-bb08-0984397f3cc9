# Trust Plus - نظام إدارة التحويلات المالية

نظام شامل لإدارة العمليات المالية والمحاسبية لشركات الصرافة والتحويلات المالية.

## المتطلبات

- **XAMPP** (Apache + MySQL + PHP)
- **PHP 7.4** أو أحدث
- **MySQL 5.7** أو أحدث
- **PDO Extension** (مفعل افتراضياً في XAMPP)

## التثبيت والإعداد

### 1. إعداد XAMPP

1. تأكد من تشغيل **Apache** و **MySQL** في XAMPP Control Panel
2. انسخ مجلد المشروع إلى `/opt/lampp/htdocs/Trust Plus`

### 2. إعداد قاعدة البيانات

1. افتح المتصفح وانتقل إلى: `http://localhost/Trust Plus/install/setup.php`
2. اضغط على "بدء إعداد قاعدة البيانات"
3. انتظر حتى اكتمال الإعداد

### 3. تسجيل الدخول الأول

بعد إكمال الإعداد، استخدم البيانات التالية لتسجيل الدخول:

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

**⚠️ مهم:** يرجى تغيير كلمة المرور فور تسجيل الدخول الأول.

## هيكل المشروع

```
Trust Plus/
├── auth/                   # نظام المصادقة
│   ├── login.php          # صفحة تسجيل الدخول
│   └── logout.php         # تسجيل الخروج
├── config/                # ملفات الإعدادات
│   └── database.php       # إعدادات قاعدة البيانات
├── dashboard/             # لوحة التحكم الرئيسية
│   └── index.php          # الصفحة الرئيسية
├── database/              # ملفات قاعدة البيانات
│   ├── trust_plus_schema.sql      # هيكل قاعدة البيانات - الجزء الأول
│   ├── trust_plus_schema_part2.sql # هيكل قاعدة البيانات - الجزء الثاني
│   └── initial_data.sql           # البيانات الأولية
├── includes/              # الملفات المشتركة
│   └── auth.php           # نظام المصادقة والصلاحيات
├── install/               # ملفات التثبيت
│   └── setup.php          # إعداد قاعدة البيانات
├── index.php              # الصفحة الرئيسية
└── README.md              # هذا الملف
```

## الميزات المتوفرة حالياً

### ✅ تم تطويرها:
- نظام المصادقة والأمان المتقدم
- إدارة المستخدمين والصلاحيات
- هيكل قاعدة البيانات الكامل
- لوحة التحكم الرئيسية
- نظام الأدوار والصلاحيات
- **إدارة العملاء مع نظام KYC/AML المتقدم**
  - تسجيل العملاء الجدد
  - البحث والفلترة المتقدمة
  - إدارة حالات KYC (قيد المراجعة، معتمد، مرفوض)
  - تقييم مستويات المخاطر (منخفض، متوسط، عالي)
  - إدارة القائمة السوداء
  - رفع وإدارة المستندات
  - فحوصات الامتثال المتعددة
  - تتبع تاريخ التحديثات
  - سجلات التدقيق الشاملة

- **وحدة عمليات الصرافة مع حساب الأرباح**
  - إنشاء عمليات صرافة تفاعلية
  - حساب تلقائي للمبالغ والعمولات
  - إدارة أسعار الصرف اليومية
  - حساب أرباح الفوارق (Spread) والعمولات
  - تقارير أرباح مفصلة مع رسوم بيانية
  - إيصالات قابلة للطباعة
  - فحص حدود المعاملات والامتثال
  - إحصائيات فورية ومتقدمة

- **نظام التحويلات المالية مع تتبع الحالات**
  - نظام خطوات متعدد لإنشاء التحويلات
  - تتبع شامل لحالات التحويل (معلق، مرسل، مستلم، مدفوع)
  - حساب تلقائي للرسوم وأرباح الصرف
  - إدارة الشركاء والممرات المالية
  - تاريخ مفصل لجميع تحديثات الحالة
  - إشعارات تلقائية للعملاء
  - إيصالات مفصلة مع تتبع الحالة
  - تقارير أرباح التحويلات
  - فحوصات امتثال متقدمة

### 🔄 قيد التطوير:
- إدارة الصناديق والبنوك
- التقارير المالية المتقدمة
- الإعدادات المتقدمة

## الأدوار والصلاحيات

### 1. مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين والأدوار
- إعدادات النظام

### 2. مدير الفرع (Manager)
- إدارة العمليات والموظفين
- اعتماد المعاملات
- التقارير الإدارية

### 3. المحاسب (Accountant)
- العمليات المحاسبية والمالية
- التقارير المالية
- إدارة الحسابات

### 4. أمين الصندوق (Cashier)
- العمليات النقدية والصرافة
- إدارة الصناديق
- المعاملات اليومية

### 5. الموظف التشغيلي (Operator)
- العمليات الأساسية
- عرض البيانات فقط

## قاعدة البيانات

### الجداول الرئيسية:
- `users` - المستخدمين
- `roles` - الأدوار
- `permissions` - الصلاحيات
- `customers` - العملاء
- `currencies` - العملات
- `exchange_rates` - أسعار الصرف
- `transactions` - المعاملات المالية
- `accounts` - دليل الحسابات
- `audit_logs` - سجلات التدقيق

### جداول نظام KYC/AML:
- `documents` - المستندات المرفوعة
- `compliance_checks` - فحوصات الامتثال
- `customer_history` - تاريخ تحديثات العملاء
- `notifications` - الإشعارات
- `system_events` - أحداث النظام

## الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية من محاولات تسجيل الدخول المتكررة
- نظام صلاحيات متقدم
- سجلات تدقيق شاملة
- حماية من SQL Injection باستخدام Prepared Statements

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تواصل مع مدير النظام
- راجع ملفات السجلات في قاعدة البيانات

## الإصدار

**الإصدار الحالي:** 1.0.0 (النسخة الأولية)

---

**ملاحظة:** هذا النظام مصمم للعمل كتطبيق مكتبي مستقل بدون تكاملات خارجية لتبادل البيانات.
