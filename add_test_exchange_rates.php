<?php
/**
 * Trust Plus - Add Test Exchange Rates
 * إضافة أسعار صرف تجريبية
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>💱 إضافة أسعار صرف تجريبية</h1>";
echo "<hr>";

try {
    // تحميل الملفات المطلوبة
    require_once 'config.php';
    require_once 'includes/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<h2>🔍 فحص العملات المتوفرة</h2>";
    
    // جلب جميع العملات النشطة
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $currencies = $stmt->fetchAll();
    
    if (count($currencies) < 2) {
        echo "<p style='color: red;'>❌ تحتاج إلى عملتين على الأقل لإضافة أسعار الصرف</p>";
        exit();
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>المعرف</th>";
    echo "<th style='padding: 10px;'>الاسم</th>";
    echo "<th style='padding: 10px;'>الرمز</th>";
    echo "<th style='padding: 10px;'>العملة الأساسية</th>";
    echo "</tr>";
    
    foreach ($currencies as $currency) {
        echo "<tr>";
        echo "<td style='padding: 8px; text-align: center;'>" . $currency['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($currency['name']) . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . $currency['code'] . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . ($currency['is_base_currency'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // العثور على العملة الأساسية
    $base_currency = null;
    foreach ($currencies as $currency) {
        if ($currency['is_base_currency']) {
            $base_currency = $currency;
            break;
        }
    }
    
    if (!$base_currency) {
        echo "<p style='color: red;'>❌ لا توجد عملة أساسية محددة</p>";
        exit();
    }
    
    echo "<p><strong>العملة الأساسية:</strong> {$base_currency['name']} ({$base_currency['code']})</p>";
    
    // الحصول على معرف المستخدم الافتراضي
    $stmt = $db->prepare("SELECT id FROM users WHERE username = 'admin' LIMIT 1");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    $user_id = $admin_user['id'] ?? 1;
    
    echo "<h2>💰 إضافة أسعار الصرف</h2>";
    
    // أسعار صرف تجريبية (مقابل العملة الأساسية)
    $test_rates = [
        'USD' => ['buy' => 1.00, 'sell' => 1.02],
        'EUR' => ['buy' => 0.85, 'sell' => 0.87],
        'GBP' => ['buy' => 0.73, 'sell' => 0.75],
        'SAR' => ['buy' => 3.75, 'sell' => 3.77],
        'AED' => ['buy' => 3.67, 'sell' => 3.69],
        'KWD' => ['buy' => 0.30, 'sell' => 0.31],
        'QAR' => ['buy' => 3.64, 'sell' => 3.66],
        'BHD' => ['buy' => 0.377, 'sell' => 0.379],
        'ILS' => ['buy' => 3.65, 'sell' => 3.70]
    ];
    
    $db->beginTransaction();
    
    try {
        $added_rates = 0;
        
        foreach ($currencies as $currency) {
            if ($currency['id'] == $base_currency['id']) {
                continue; // تخطي العملة الأساسية
            }
            
            $currency_code = $currency['code'];
            
            if (!isset($test_rates[$currency_code])) {
                echo "<p style='color: orange;'>⚠️ لا توجد أسعار تجريبية للعملة $currency_code</p>";
                continue;
            }
            
            $rates = $test_rates[$currency_code];
            
            // حذف الأسعار القديمة لهذا اليوم
            $stmt = $db->prepare("
                DELETE FROM exchange_rates 
                WHERE ((from_currency_id = ? AND to_currency_id = ?) OR 
                       (from_currency_id = ? AND to_currency_id = ?))
                  AND effective_date = CURDATE()
            ");
            $stmt->execute([
                $base_currency['id'], $currency['id'],
                $currency['id'], $base_currency['id']
            ]);
            
            // إضافة سعر من العملة الأساسية إلى العملة الأجنبية
            $stmt = $db->prepare("
                INSERT INTO exchange_rates (from_currency_id, to_currency_id, buy_rate, sell_rate, effective_date, user_id, is_active)
                VALUES (?, ?, ?, ?, CURDATE(), ?, 1)
            ");
            $stmt->execute([
                $base_currency['id'], 
                $currency['id'], 
                $rates['buy'], 
                $rates['sell'], 
                $user_id
            ]);
            
            // إضافة سعر من العملة الأجنبية إلى العملة الأساسية
            $buy_rate_reverse = 1 / $rates['sell'];
            $sell_rate_reverse = 1 / $rates['buy'];
            
            $stmt->execute([
                $currency['id'], 
                $base_currency['id'], 
                $buy_rate_reverse, 
                $sell_rate_reverse, 
                $user_id
            ]);
            
            echo "<p>✅ تم إضافة أسعار صرف {$base_currency['code']} ↔ $currency_code</p>";
            echo "<p style='margin-left: 20px;'>";
            echo "• {$base_currency['code']} → $currency_code: شراء {$rates['buy']}, بيع {$rates['sell']}<br>";
            echo "• $currency_code → {$base_currency['code']}: شراء " . number_format($buy_rate_reverse, 6) . ", بيع " . number_format($sell_rate_reverse, 6);
            echo "</p>";
            
            $added_rates += 2;
        }
        
        $db->commit();
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
        echo "<h2>🎉 تم إضافة أسعار الصرف بنجاح!</h2>";
        echo "<p><strong>إجمالي الأسعار المضافة:</strong> $added_rates</p>";
        echo "<p><strong>تاريخ السريان:</strong> " . date('Y-m-d') . "</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        $db->rollBack();
        throw $e;
    }
    
    // عرض جميع أسعار الصرف الحالية
    echo "<h2>📊 أسعار الصرف الحالية</h2>";
    
    $stmt = $db->prepare("
        SELECT er.*, 
               c1.name as from_currency_name, c1.code as from_currency_code,
               c2.name as to_currency_name, c2.code as to_currency_code
        FROM exchange_rates er
        JOIN currencies c1 ON er.from_currency_id = c1.id
        JOIN currencies c2 ON er.to_currency_id = c2.id
        WHERE er.is_active = 1 AND er.effective_date = CURDATE()
        ORDER BY c1.code, c2.code
    ");
    $stmt->execute();
    $current_rates = $stmt->fetchAll();
    
    if (count($current_rates) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>من</th>";
        echo "<th style='padding: 10px;'>إلى</th>";
        echo "<th style='padding: 10px;'>سعر الشراء</th>";
        echo "<th style='padding: 10px;'>سعر البيع</th>";
        echo "<th style='padding: 10px;'>تاريخ السريان</th>";
        echo "</tr>";
        
        foreach ($current_rates as $rate) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$rate['from_currency_code']}</td>";
            echo "<td style='padding: 8px;'>{$rate['to_currency_code']}</td>";
            echo "<td style='padding: 8px; text-align: right;'>" . number_format($rate['buy_rate'], 6) . "</td>";
            echo "<td style='padding: 8px; text-align: right;'>" . number_format($rate['sell_rate'], 6) . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>{$rate['effective_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>إجمالي أسعار الصرف النشطة:</strong> " . count($current_rates) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد أسعار صرف نشطة</p>";
    }
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔗 اختبر النظام الآن:</h3>";
    echo "<p><a href='test_api.php' target='_blank' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 اختبار API</a></p>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في إضافة أسعار الصرف</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    font-size: 0.9em;
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
