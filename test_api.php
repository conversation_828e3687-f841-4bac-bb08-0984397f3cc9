<?php
/**
 * Trust Plus - Test API Endpoints
 * اختبار نقاط النهاية للـ API
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 اختبار API للصرافة</h1>";
echo "<hr>";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل دخول
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['permissions'] = ['exchange.view', 'exchange.create', 'customers.view'];

echo "<h2>🔍 اختبار get_exchange_rate.php</h2>";

// اختبار 1: طلب صحيح
$test_data = [
    'from_currency_id' => 1,
    'to_currency_id' => 2,
    'amount' => 100
];

echo "<h3>📤 إرسال طلب صحيح:</h3>";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/Trust%20Plus/dashboard/get_exchange_rate.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: ' . session_name() . '=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>📥 الاستجابة:</h3>";
echo "<p><strong>HTTP Code:</strong> $http_code</p>";

if ($error) {
    echo "<p style='color: red;'><strong>خطأ cURL:</strong> $error</p>";
} else {
    echo "<p><strong>الاستجابة الخام:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $json_response = json_decode($response, true);
    if ($json_response !== null) {
        echo "<p style='color: green;'>✅ JSON صحيح</p>";
        echo "<pre>" . json_encode($json_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p style='color: red;'>❌ JSON غير صحيح</p>";
        echo "<p><strong>خطأ JSON:</strong> " . json_last_error_msg() . "</p>";
    }
}

echo "<hr>";

echo "<h2>🔍 اختبار search_customers.php</h2>";

echo "<h3>📤 البحث عن عميل:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/Trust%20Plus/dashboard/search_customers.php?q=test');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Cookie: ' . session_name() . '=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>📥 الاستجابة:</h3>";
echo "<p><strong>HTTP Code:</strong> $http_code</p>";

if ($error) {
    echo "<p style='color: red;'><strong>خطأ cURL:</strong> $error</p>";
} else {
    echo "<p><strong>الاستجابة الخام:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $json_response = json_decode($response, true);
    if ($json_response !== null) {
        echo "<p style='color: green;'>✅ JSON صحيح</p>";
        echo "<pre>" . json_encode($json_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p style='color: red;'>❌ JSON غير صحيح</p>";
        echo "<p><strong>خطأ JSON:</strong> " . json_last_error_msg() . "</p>";
    }
}

echo "<hr>";

echo "<h2>🔍 اختبار قاعدة البيانات</h2>";

try {
    require_once 'config.php';
    require_once 'includes/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار جدول العملات
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM currencies WHERE is_active = 1");
    $stmt->execute();
    $currency_count = $stmt->fetch()['count'];
    
    echo "<p><strong>عدد العملات النشطة:</strong> $currency_count</p>";
    
    if ($currency_count < 2) {
        echo "<p style='color: orange;'>⚠️ تحتاج إلى عملتين على الأقل لاختبار الصرافة</p>";
    }
    
    // اختبار جدول أسعار الصرف
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM exchange_rates WHERE is_active = 1");
    $stmt->execute();
    $rates_count = $stmt->fetch()['count'];
    
    echo "<p><strong>عدد أسعار الصرف النشطة:</strong> $rates_count</p>";
    
    if ($rates_count == 0) {
        echo "<p style='color: red;'>❌ لا توجد أسعار صرف نشطة</p>";
    }
    
    // اختبار جدول العملاء
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
    $stmt->execute();
    $customers_count = $stmt->fetch()['count'];
    
    echo "<p><strong>عدد العملاء النشطين:</strong> $customers_count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";

echo "<h2>🔍 اختبار الملفات المطلوبة</h2>";

$required_files = [
    'includes/auth.php',
    'includes/exchange_manager.php',
    'includes/customer_manager.php',
    'includes/database.php',
    'config.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $file مفقود</p>";
    }
}

echo "<hr>";

echo "<h2>📊 ملخص الاختبار</h2>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 خطوات الإصلاح المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ إصلاح output buffering في get_exchange_rate.php</li>";
echo "<li>✅ إصلاح output buffering في search_customers.php</li>";
echo "<li>✅ إضافة JSON_UNESCAPED_UNICODE لدعم النصوص العربية</li>";
echo "<li>✅ تنظيف جميع نقاط الخروج من الملفات</li>";
echo "<li>✅ إضافة headers صحيحة للـ JSON</li>";
echo "</ul>";

echo "<h3>🧪 نتائج الاختبار:</h3>";
echo "<p>تحقق من النتائج أعلاه لمعرفة حالة كل API</p>";

echo "<h3>🔗 اختبر النظام الآن:</h3>";
echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
echo "</div>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

pre {
    font-size: 0.9em;
    line-height: 1.4;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    line-height: 1.6;
}
</style>
