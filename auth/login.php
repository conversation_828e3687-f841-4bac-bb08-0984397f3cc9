<?php
// تضمين ملف التكوين أولاً
if (file_exists('../config.php')) {
    require_once '../config.php';
}

require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();
$error_message = '';
$success_message = '';
$warning_message = '';
$info_message = '';

// التحقق من وجود رسالة في URL
if (isset($_GET['message'])) {
    $message = $_GET['message'];
    $reason = $_GET['reason'] ?? 'manual';

    // تحديد نوع الرسالة حسب السبب
    switch ($reason) {
        case 'session_timeout':
        case 'max_session_exceeded':
            $warning_message = $message;
            break;
        case 'security':
            $error_message = $message;
            break;
        case 'no_session':
            $info_message = $message;
            break;
        default:
            $success_message = $message;
            break;
    }
}

// التحقق من وجود جلسة نشطة
if ($auth->checkSession()) {
    header('Location: ../dashboard/index.php');
    exit();
}

// معالجة طلب تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = $auth->login($username, $password);

        if ($result['success']) {
            // التحقق من نجاح إنشاء الجلسة
            if ($auth->checkSession()) {
                // إعادة التوجيه مع تأخير قصير للتأكد من حفظ الجلسة
                echo '<script>
                    setTimeout(function() {
                        window.location.href = "../dashboard/index.php";
                    }, 100);
                </script>';

                // إعادة توجيه احتياطية
                header('Location: ../dashboard/index.php');
                exit();
            } else {
                $error_message = 'فشل في إنشاء الجلسة. يرجى المحاولة مرة أخرى.';
            }
        } else {
            $error_message = $result['message'];
        }
    }
}

// إعدادات الصفحة
$page_type = 'forms';
$page_title = 'تسجيل الدخول - ' . (defined('SYSTEM_NAME') ? SYSTEM_NAME : 'Trust Plus');
$hide_sidebar = true;
$hide_navbar = true;
$hide_footer = true;
$hide_preloader = true;

// إضافة CSS خاص بصفحة تسجيل الدخول
$additional_head = '
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .login-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 400px;
        width: 100%;
        margin: 2rem;
    }

    .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .login-header h2 {
        margin: 0;
        font-weight: 600;
    }

    .login-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }

    .login-body {
        padding: 2rem;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .input-group-text {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-left: none;
    }

    .form-control.with-icon {
        border-right: none;
    }
</style>
';

include '../includes/header.php';
?>
<!-- صفحة تسجيل الدخول -->
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-shield-alt me-2"></i>Trust Plus</h2>
            <p>نظام إدارة التحويلات المالية</p>
        </div>
        
        <div class="login-body">
            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($warning_message): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-clock me-2"></i>
                    <?php echo htmlspecialchars($warning_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($info_message): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo htmlspecialchars($info_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" 
                               class="form-control with-icon" 
                               id="username" 
                               name="username" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               required 
                               autocomplete="username">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" 
                               class="form-control with-icon" 
                               id="password" 
                               name="password" 
                               required 
                               autocomplete="current-password">
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    للحصول على المساعدة، اتصل بمدير النظام
                </small>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript خاص بصفحة تسجيل الدخول
$inline_js = '
    // التركيز على حقل اسم المستخدم عند تحميل الصفحة
    document.addEventListener("DOMContentLoaded", function() {
        document.getElementById("username").focus();
    });
';

include '../includes/footer.php';
?>
