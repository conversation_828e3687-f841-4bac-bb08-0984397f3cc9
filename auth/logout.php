<?php
/**
 * Trust Plus - Logout Handler
 * معالج تسجيل الخروج
 */

require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// الحصول على معلومات المستخدم قبل تسجيل الخروج
$current_user = $auth->getCurrentUser();
$username = $current_user['full_name'] ?? $current_user['username'] ?? 'المستخدم';

// تحديد سبب تسجيل الخروج
$reason = $_GET['reason'] ?? 'manual';
$logout_messages = [
    'manual' => 'تم تسجيل الخروج بنجاح',
    'session_timeout' => 'تم تسجيل الخروج تلقائياً بسبب انتهاء صلاحية الجلسة',
    'max_session_exceeded' => 'تم تسجيل الخروج تلقائياً بسبب تجاوز الحد الأقصى لمدة الجلسة',
    'security' => 'تم تسجيل الخروج لأسباب أمنية',
    'admin_logout' => 'تم تسجيل الخروج من قبل المدير'
];

$message = $logout_messages[$reason] ?? $logout_messages['manual'];

// تسجيل الخروج
$session_info = $auth->logout($reason);

// إنشاء رسالة مخصصة
if ($username && $username !== 'المستخدم') {
    $message = "وداعاً $username، " . $message;
}

// إضافة معلومات إضافية حسب السبب
switch ($reason) {
    case 'session_timeout':
        $message .= '. يرجى تسجيل الدخول مرة أخرى للمتابعة.';
        break;
    case 'max_session_exceeded':
        $message .= '. تم تجاوز الحد الأقصى المسموح لمدة الجلسة.';
        break;
    case 'security':
        $message .= '. تم اكتشاف نشاط مشبوه.';
        break;
}

// إعادة التوجيه مع الرسالة
$redirect_url = 'login.php?message=' . urlencode($message);

// إضافة معلومات إضافية للـ URL إذا لزم الأمر
if ($reason !== 'manual') {
    $redirect_url .= '&reason=' . urlencode($reason);
}

header('Location: ' . $redirect_url);
exit();
?>
