<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_manager.php';

$auth = new Auth();
$exchangeManager = new ExchangeManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.rates')) {
    header('Location: exchange.php?error=ليس لديك صلاحية لإدارة أسعار الصرف');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'default';
$page_title = 'أسعار الصرف - ' . SYSTEM_NAME;
$page_header = 'أسعار الصرف';
$page_subtitle = 'إدارة وتحديث أسعار صرف العملات';
$page_icon = 'fas fa-chart-line';
$show_breadcrumb = true;

$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب العملات النشطة
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $currencies = $stmt->fetchAll();
    
    // جلب أسعار الصرف الحالية
    $stmt = $db->prepare("
        SELECT er.*, 
               c1.name as from_currency_name, c1.code as from_currency_code, c1.symbol as from_currency_symbol,
               c2.name as to_currency_name, c2.code as to_currency_code, c2.symbol as to_currency_symbol,
               u.full_name as updated_by_name
        FROM exchange_rates er
        JOIN currencies c1 ON er.from_currency_id = c1.id
        JOIN currencies c2 ON er.to_currency_id = c2.id
        JOIN users u ON er.user_id = u.id
        WHERE er.is_active = 1 AND er.effective_date = CURDATE()
        ORDER BY c1.code, c2.code
    ");
    $stmt->execute();
    $current_rates = $stmt->fetchAll();
    
} catch (Exception $e) {
    $currencies = [];
    $current_rates = [];
    $error_message = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// معالجة تحديث أسعار الصرف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_rates') {
    $rates_data = [];
    
    foreach ($_POST['rates'] as $rate_id => $rate_info) {
        if (!empty($rate_info['buy_rate']) && !empty($rate_info['sell_rate'])) {
            $rates_data[] = [
                'from_currency_id' => intval($rate_info['from_currency_id']),
                'to_currency_id' => intval($rate_info['to_currency_id']),
                'buy_rate' => floatval($rate_info['buy_rate']),
                'sell_rate' => floatval($rate_info['sell_rate'])
            ];
        }
    }
    
    if (!empty($rates_data)) {
        $result = $exchangeManager->updateExchangeRates($rates_data, $current_user['id']);
        
        if ($result['success']) {
            $success_message = $result['message'];
            // إعادة جلب الأسعار المحدثة
            $stmt = $db->prepare("
                SELECT er.*, 
                       c1.name as from_currency_name, c1.code as from_currency_code, c1.symbol as from_currency_symbol,
                       c2.name as to_currency_name, c2.code as to_currency_code, c2.symbol as to_currency_symbol,
                       u.full_name as updated_by_name
                FROM exchange_rates er
                JOIN currencies c1 ON er.from_currency_id = c1.id
                JOIN currencies c2 ON er.to_currency_id = c2.id
                JOIN users u ON er.user_id = u.id
                WHERE er.is_active = 1 AND er.effective_date = CURDATE()
                ORDER BY c1.code, c2.code
            ");
            $stmt->execute();
            $current_rates = $stmt->fetchAll();
        } else {
            $error_message = $result['message'];
        }
    } else {
        $error_message = 'لم يتم تحديد أي أسعار صرف صحيحة';
    }
}

include '../includes/header.php';
?>
<!-- صفحة أسعار الصرف -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">إدارة أسعار الصرف</h2>
                        <p class="text-muted">تحديث أسعار الصرف اليومية وإدارة الهوامش</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" onclick="autoCalculateRates()">
                            <i class="fas fa-calculator me-2"></i>
                            حساب تلقائي
                        </button>
                        <a href="exchange.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للصرافة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- إجراءات سريعة -->
        <div class="quick-actions">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label small">تطبيق هامش موحد (%)</label>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control" id="uniformSpread" step="0.01" placeholder="0.50">
                        <button class="btn btn-outline-primary" onclick="applyUniformSpread()">تطبيق</button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label small">زيادة جميع الأسعار (%)</label>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control" id="priceIncrease" step="0.01" placeholder="1.00">
                        <button class="btn btn-outline-warning" onclick="increaseAllRates()">زيادة</button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label small">تقليل جميع الأسعار (%)</label>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control" id="priceDecrease" step="0.01" placeholder="1.00">
                        <button class="btn btn-outline-danger" onclick="decreaseAllRates()">تقليل</button>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-end h-100">
                        <button class="btn btn-info btn-sm w-100" onclick="resetToMarketRates()">
                            <i class="fas fa-refresh me-1"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول أسعار الصرف -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    أسعار الصرف الحالية - <?php echo date('Y-m-d'); ?>
                </h5>
                <div>
                    <span class="badge bg-light text-dark">
                        آخر تحديث: <?php echo date('H:i'); ?>
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <form method="POST" id="ratesForm">
                    <input type="hidden" name="action" value="update_rates">
                    
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>زوج العملة</th>
                                    <th>سعر الشراء</th>
                                    <th>سعر البيع</th>
                                    <th>الفارق</th>
                                    <th>الهامش %</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($current_rates)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center p-4">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">لا توجد أسعار صرف</h6>
                                            <p class="text-muted">لم يتم تحديد أسعار صرف لهذا اليوم</p>
                                            <button type="button" class="btn btn-primary" onclick="addNewRate()">
                                                <i class="fas fa-plus me-2"></i>
                                                إضافة سعر صرف
                                            </button>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($current_rates as $index => $rate): ?>
                                    <tr>
                                        <td>
                                            <div class="currency-pair">
                                                <?php echo htmlspecialchars($rate['from_currency_code']); ?>/<?php echo htmlspecialchars($rate['to_currency_code']); ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($rate['from_currency_name']); ?> → <?php echo htmlspecialchars($rate['to_currency_name']); ?>
                                            </small>
                                            <input type="hidden" name="rates[<?php echo $index; ?>][from_currency_id]" value="<?php echo $rate['from_currency_id']; ?>">
                                            <input type="hidden" name="rates[<?php echo $index; ?>][to_currency_id]" value="<?php echo $rate['to_currency_id']; ?>">
                                        </td>
                                        <td>
                                            <input type="number" 
                                                   class="form-control rate-input buy-rate" 
                                                   name="rates[<?php echo $index; ?>][buy_rate]" 
                                                   value="<?php echo $rate['buy_rate']; ?>"
                                                   step="0.000001"
                                                   data-original="<?php echo $rate['buy_rate']; ?>"
                                                   onchange="calculateSpread(this)">
                                        </td>
                                        <td>
                                            <input type="number" 
                                                   class="form-control rate-input sell-rate" 
                                                   name="rates[<?php echo $index; ?>][sell_rate]" 
                                                   value="<?php echo $rate['sell_rate']; ?>"
                                                   step="0.000001"
                                                   data-original="<?php echo $rate['sell_rate']; ?>"
                                                   onchange="calculateSpread(this)">
                                        </td>
                                        <td>
                                            <span class="spread-value fw-bold" data-row="<?php echo $index; ?>">
                                                <?php echo number_format(abs($rate['sell_rate'] - $rate['buy_rate']), 6); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="spread-percentage text-success" data-row="<?php echo $index; ?>">
                                                <?php 
                                                $spread_percentage = (abs($rate['sell_rate'] - $rate['buy_rate']) / $rate['buy_rate']) * 100;
                                                echo number_format($spread_percentage, 2); 
                                                ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <div class="last-updated">
                                                <?php echo date('H:i', strtotime($rate['created_at'])); ?>
                                            </div>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($rate['updated_by_name']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-info" onclick="copyRate(<?php echo $index; ?>)">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning" onclick="swapRates(<?php echo $index; ?>)">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success" onclick="autoAdjust(<?php echo $index; ?>)">
                                                    <i class="fas fa-magic"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if (!empty($current_rates)): ?>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    تأكد من مراجعة جميع الأسعار قبل الحفظ
                                </small>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ أسعار الصرف
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // حساب الفارق والهامش
        function calculateSpread(input) {
            const row = input.closest('tr');
            const buyRate = parseFloat(row.querySelector('.buy-rate').value) || 0;
            const sellRate = parseFloat(row.querySelector('.sell-rate').value) || 0;
            
            if (buyRate > 0 && sellRate > 0) {
                const spread = Math.abs(sellRate - buyRate);
                const spreadPercentage = (spread / buyRate) * 100;
                
                const rowIndex = input.name.match(/\[(\d+)\]/)[1];
                document.querySelector(`[data-row="${rowIndex}"].spread-value`).textContent = spread.toFixed(6);
                document.querySelector(`[data-row="${rowIndex}"].spread-percentage`).textContent = spreadPercentage.toFixed(2) + '%';
            }
        }

        // تطبيق هامش موحد
        function applyUniformSpread() {
            const spreadPercent = parseFloat(document.getElementById('uniformSpread').value) || 0;
            
            if (spreadPercent <= 0) {
                alert('يرجى إدخال نسبة هامش صحيحة');
                return;
            }
            
            document.querySelectorAll('.buy-rate').forEach(input => {
                const buyRate = parseFloat(input.value) || 0;
                if (buyRate > 0) {
                    const sellRate = buyRate * (1 + spreadPercent / 100);
                    const row = input.closest('tr');
                    row.querySelector('.sell-rate').value = sellRate.toFixed(6);
                    calculateSpread(input);
                }
            });
        }

        // زيادة جميع الأسعار
        function increaseAllRates() {
            const increasePercent = parseFloat(document.getElementById('priceIncrease').value) || 0;
            
            if (increasePercent <= 0) {
                alert('يرجى إدخال نسبة زيادة صحيحة');
                return;
            }
            
            document.querySelectorAll('.buy-rate, .sell-rate').forEach(input => {
                const currentRate = parseFloat(input.value) || 0;
                if (currentRate > 0) {
                    const newRate = currentRate * (1 + increasePercent / 100);
                    input.value = newRate.toFixed(6);
                    calculateSpread(input);
                }
            });
        }

        // تقليل جميع الأسعار
        function decreaseAllRates() {
            const decreasePercent = parseFloat(document.getElementById('priceDecrease').value) || 0;
            
            if (decreasePercent <= 0) {
                alert('يرجى إدخال نسبة تقليل صحيحة');
                return;
            }
            
            document.querySelectorAll('.buy-rate, .sell-rate').forEach(input => {
                const currentRate = parseFloat(input.value) || 0;
                if (currentRate > 0) {
                    const newRate = currentRate * (1 - decreasePercent / 100);
                    input.value = newRate.toFixed(6);
                    calculateSpread(input);
                }
            });
        }

        // نسخ السعر
        function copyRate(rowIndex) {
            const row = document.querySelector(`input[name="rates[${rowIndex}][buy_rate]"]`).closest('tr');
            const buyRate = row.querySelector('.buy-rate').value;
            const sellRate = row.querySelector('.sell-rate').value;
            
            navigator.clipboard.writeText(`Buy: ${buyRate}, Sell: ${sellRate}`).then(() => {
                alert('تم نسخ السعر');
            });
        }

        // تبديل أسعار الشراء والبيع
        function swapRates(rowIndex) {
            const row = document.querySelector(`input[name="rates[${rowIndex}][buy_rate]"]`).closest('tr');
            const buyRateInput = row.querySelector('.buy-rate');
            const sellRateInput = row.querySelector('.sell-rate');
            
            const tempValue = buyRateInput.value;
            buyRateInput.value = sellRateInput.value;
            sellRateInput.value = tempValue;
            
            calculateSpread(buyRateInput);
        }

        // تعديل تلقائي
        function autoAdjust(rowIndex) {
            // يمكن تطوير هذه الدالة لتطبيق خوارزمية تعديل تلقائي
            alert('ميزة التعديل التلقائي قيد التطوير');
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                document.querySelectorAll('.buy-rate, .sell-rate').forEach(input => {
                    input.value = input.dataset.original;
                    calculateSpread(input);
                });
            }
        }

        // حساب تلقائي (يمكن ربطه بـ API خارجي)
        function autoCalculateRates() {
            alert('ميزة الحساب التلقائي قيد التطوير - يمكن ربطها بمصادر أسعار خارجية');
        }

        // إعادة تعيين لأسعار السوق
        function resetToMarketRates() {
            alert('ميزة إعادة التعيين لأسعار السوق قيد التطوير');
        }

        // إضافة سعر صرف جديد
        function addNewRate() {
            alert('ميزة إضافة أسعار صرف جديدة قيد التطوير');
        }

        // التحقق من النموذج قبل الإرسال
        document.getElementById('ratesForm').addEventListener('submit', function(e) {
            const rates = document.querySelectorAll('.buy-rate, .sell-rate');
            let hasValidRates = false;
            
            rates.forEach(input => {
                if (parseFloat(input.value) > 0) {
                    hasValidRates = true;
                }
            });
            
            if (!hasValidRates) {
                e.preventDefault();
                alert('يرجى إدخال أسعار صرف صحيحة');
                return;
            }
            
            if (!confirm('هل أنت متأكد من تحديث أسعار الصرف؟')) {
                e.preventDefault();
            }
        });

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>

<?php include '../includes/footer.php'; ?>
