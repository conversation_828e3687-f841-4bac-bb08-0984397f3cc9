<?php
require_once '../includes/auth.php';
require_once '../includes/transfer_manager.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$transferManager = new TransferManager();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('transfers.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'default';
$page_title = 'التحويلات المالية - ' . SYSTEM_NAME;
$page_header = 'التحويلات المالية';
$page_subtitle = 'إدارة التحويلات المحلية والدولية';
$page_icon = 'fas fa-paper-plane';
$show_breadcrumb = true;

$error_message = '';
$success_message = '';

// جلب البيانات الأساسية
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب العملات النشطة
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $currencies = $stmt->fetchAll();
    
    // جلب البلدان المدعومة
    $stmt = $db->prepare("SELECT DISTINCT to_country FROM transfer_settings WHERE is_active = 1 ORDER BY to_country");
    $stmt->execute();
    $supported_countries = $stmt->fetchAll();
    
    // جلب التحويلات الأخيرة
    $recent_transfers = $transferManager->searchTransfers(['branch_id' => $current_user['branch_id']], 20, 0);
    
    // جلب إحصائيات اليوم
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as today_transfers,
            SUM(ft.sending_amount) as today_volume,
            SUM(ft.transfer_fee_amount) as today_fees,
            COUNT(CASE WHEN ft.status = 'pending' THEN 1 END) as pending_transfers,
            COUNT(CASE WHEN ft.status = 'sent' THEN 1 END) as sent_transfers,
            COUNT(CASE WHEN ft.status = 'received' THEN 1 END) as received_transfers,
            COUNT(CASE WHEN ft.status = 'paid' THEN 1 END) as paid_transfers
        FROM transactions t
        JOIN financial_transfers ft ON t.id = ft.transaction_id
        WHERE t.transaction_type = 'transfer' 
          AND DATE(t.transaction_date) = CURDATE()
          AND t.branch_id = :branch_id
    ");
    $stmt->bindParam(':branch_id', $current_user['branch_id']);
    $stmt->execute();
    $today_stats = $stmt->fetch();
    
    // جلب أهم الممرات (Corridors)
    $stmt = $db->prepare("
        SELECT 
            CONCAT(sc.code, '_', rc.code) as corridor,
            CONCAT(sc.name, ' → ', rc.name) as corridor_name,
            COUNT(*) as transfer_count,
            SUM(ft.sending_amount) as total_volume
        FROM financial_transfers ft
        JOIN currencies sc ON ft.sending_currency_id = sc.id
        JOIN currencies rc ON ft.receiving_currency_id = rc.id
        JOIN transactions t ON ft.transaction_id = t.id
        WHERE DATE(t.transaction_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
          AND t.branch_id = :branch_id
        GROUP BY ft.sending_currency_id, ft.receiving_currency_id
        ORDER BY transfer_count DESC
        LIMIT 5
    ");
    $stmt->bindParam(':branch_id', $current_user['branch_id']);
    $stmt->execute();
    $top_corridors = $stmt->fetchAll();
    
} catch (Exception $e) {
    $currencies = [];
    $supported_countries = [];
    $recent_transfers = [];
    $today_stats = [
        'today_transfers' => 0, 'today_volume' => 0, 'today_fees' => 0,
        'pending_transfers' => 0, 'sent_transfers' => 0, 'received_transfers' => 0, 'paid_transfers' => 0
    ];
    $top_corridors = [];
}

// معالجة البحث عن العملاء
$customer_search = '';
$customers = [];
if (isset($_GET['search_customer']) && !empty($_GET['search_customer'])) {
    $customer_search = trim($_GET['search_customer']);
    $customers = $customerManager->searchCustomers($customer_search, [], 10, 0);
}

include '../includes/header.php';
?>
<!-- صفحة التحويلات المالية -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">التحويلات المالية</h2>
                        <p class="text-muted">إدارة التحويلات المالية مع تتبع الحالات</p>
                    </div>
                    <div>
                        <?php if ($auth->hasPermission('transfers.approve')): ?>
                        <a href="transfer_approvals.php" class="btn btn-outline-warning me-2">
                            <i class="fas fa-check-circle me-2"></i>
                            الموافقات المعلقة
                        </a>
                        <?php endif; ?>
                        <a href="transfer_history.php" class="btn btn-outline-info me-2">
                            <i class="fas fa-history me-2"></i>
                            سجل التحويلات
                        </a>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات اليوم -->
        <div class="row mb-4">
            <div class="col-md-2-4">
                <div class="stats-card">
                    <div class="stats-icon bg-success-gradient mx-auto">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0"><?php echo number_format($today_stats['today_transfers']); ?></h3>
                        <p class="text-muted mb-0">تحويلات اليوم</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2-4">
                <div class="stats-card">
                    <div class="stats-icon bg-warning-gradient mx-auto">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo number_format($today_stats['today_volume'], 2); ?></h3>
                        <p class="text-muted mb-0">حجم التحويلات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2-4">
                <div class="stats-card">
                    <div class="stats-icon bg-info-gradient mx-auto">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo number_format($today_stats['today_fees'], 2); ?></h3>
                        <p class="text-muted mb-0">الرسوم المحصلة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2-4">
                <div class="stats-card">
                    <div class="stats-icon bg-primary-gradient mx-auto">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0"><?php echo number_format($today_stats['pending_transfers']); ?></h3>
                        <p class="text-muted mb-0">معلقة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2-4">
                <div class="stats-card">
                    <div class="stats-icon bg-secondary-gradient mx-auto">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0"><?php echo number_format($today_stats['paid_transfers']); ?></h3>
                        <p class="text-muted mb-0">مكتملة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إنشاء تحويل جديد -->
            <div class="col-lg-8">
                <?php if ($auth->hasPermission('transfers.create')): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            تحويل مالي جديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- مؤشر الخطوات -->
                        <div class="step-indicator">
                            <div class="step active" id="step1">
                                <div class="step-circle">1</div>
                                <div class="step-label">بيانات المرسل</div>
                            </div>
                            <div class="step" id="step2">
                                <div class="step-circle">2</div>
                                <div class="step-label">بيانات المستفيد</div>
                            </div>
                            <div class="step" id="step3">
                                <div class="step-circle">3</div>
                                <div class="step-label">تفاصيل التحويل</div>
                            </div>
                            <div class="step" id="step4">
                                <div class="step-circle">4</div>
                                <div class="step-label">المراجعة والتأكيد</div>
                            </div>
                        </div>

                        <form id="transferForm" action="process_transfer.php" method="POST">
                            <!-- الخطوة 1: بيانات المرسل -->
                            <div class="step-content" id="step1-content">
                                <h6 class="text-primary mb-3">الخطوة 1: بيانات المرسل</h6>

                                <div class="mb-3">
                                    <label class="form-label">العميل المرسل *</label>
                                    <div class="input-group">
                                        <input type="text"
                                               class="form-control"
                                               id="senderSearch"
                                               placeholder="ابحث عن العميل بالاسم أو رقم الهوية"
                                               autocomplete="off">
                                        <button type="button" class="btn btn-outline-secondary" id="searchSenderBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <input type="hidden" id="selectedSenderId" name="sender_customer_id" required>
                                    <div id="senderResults" class="mt-2"></div>
                                    <div id="selectedSender" class="mt-2" style="display: none;">
                                        <div class="alert alert-info">
                                            <strong>المرسل:</strong> <span id="selectedSenderName"></span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary float-end" id="changeSender">
                                                تغيير
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                        التالي <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- الخطوة 2: بيانات المستفيد -->
                            <div class="step-content" id="step2-content" style="display: none;">
                                <h6 class="text-primary mb-3">الخطوة 2: بيانات المستفيد</h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المستفيد *</label>
                                            <input type="text" class="form-control" name="recipient_name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البلد *</label>
                                            <select class="form-control" name="recipient_country" id="recipientCountry" required>
                                                <option value="">اختر البلد</option>
                                                <?php foreach ($supported_countries as $country): ?>
                                                    <option value="<?php echo htmlspecialchars($country['to_country']); ?>">
                                                        <?php echo htmlspecialchars($country['to_country']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تفاصيل الحساب</label>
                                    <textarea class="form-control" name="recipient_account_details" rows="3"
                                              placeholder="رقم الحساب، رقم الهاتف، أو تفاصيل أخرى للاستلام"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البنك أو الوكيل</label>
                                    <input type="text" class="form-control" name="recipient_bank"
                                           placeholder="اسم البنك أو الوكيل المستلم">
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                        <i class="fas fa-arrow-right me-2"></i> السابق
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                                        التالي <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- الخطوة 3: تفاصيل التحويل -->
                            <div class="step-content" id="step3-content" style="display: none;">
                                <h6 class="text-primary mb-3">الخطوة 3: تفاصيل التحويل</h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">عملة الإرسال *</label>
                                            <select class="form-control" id="sendingCurrency" name="sending_currency_id" required>
                                                <option value="">اختر العملة</option>
                                                <?php foreach ($currencies as $currency): ?>
                                                    <option value="<?php echo $currency['id']; ?>"
                                                            data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                            data-code="<?php echo htmlspecialchars($currency['code']); ?>">
                                                        <?php echo htmlspecialchars($currency['name']) . ' (' . htmlspecialchars($currency['code']) . ')'; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">عملة الاستلام *</label>
                                            <select class="form-control" id="receivingCurrency" name="receiving_currency_id" required>
                                                <option value="">اختر العملة</option>
                                                <?php foreach ($currencies as $currency): ?>
                                                    <option value="<?php echo $currency['id']; ?>"
                                                            data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                            data-code="<?php echo htmlspecialchars($currency['code']); ?>">
                                                        <?php echo htmlspecialchars($currency['name']) . ' (' . htmlspecialchars($currency['code']) . ')'; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مبلغ الإرسال *</label>
                                            <div class="currency-input">
                                                <input type="number"
                                                       class="form-control"
                                                       id="sendingAmount"
                                                       name="sending_amount"
                                                       step="0.01"
                                                       min="1"
                                                       placeholder="0.00"
                                                       required>
                                                <span class="currency-symbol" id="sendingSymbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">طريقة التحويل *</label>
                                            <select class="form-control" name="transfer_method" required>
                                                <option value="">اختر الطريقة</option>
                                                <option value="cash">استلام نقدي</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="online">خدمة إلكترونية</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- عرض حساب التحويل -->
                                <div class="rate-display" id="transferCalculation" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small>المبلغ المستلم</small>
                                            <div class="h5 mb-0" id="receivingAmount">-</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small>سعر الصرف</small>
                                            <div class="h5 mb-0" id="exchangeRate">-</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small>رسوم التحويل</small>
                                            <div class="h5 mb-0" id="transferFee">-</div>
                                        </div>
                                        <div class="col-md-3">
                                            <small>إجمالي المطلوب</small>
                                            <div class="h5 mb-0" id="totalRequired">-</div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="branch_id" value="<?php echo $current_user['branch_id']; ?>">

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                        <i class="fas fa-arrow-right me-2"></i> السابق
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                                        التالي <i class="fas fa-arrow-left ms-2"></i>
                                    </button>
                                </div>
                            <!-- الخطوة 4: المراجعة والتأكيد -->
                            <div class="step-content" id="step4-content" style="display: none;">
                                <h6 class="text-primary mb-3">الخطوة 4: المراجعة والتأكيد</h6>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>ملخص التحويل</h6>
                                    <div id="transferSummary">
                                        <!-- سيتم ملء هذا القسم بـ JavaScript -->
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirmTransfer" required>
                                    <label class="form-check-label" for="confirmTransfer">
                                        أؤكد صحة جميع البيانات المدخلة وأوافق على شروط وأحكام التحويل
                                    </label>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)">
                                        <i class="fas fa-arrow-right me-2"></i> السابق
                                    </button>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        تنفيذ التحويل
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ليس لديك صلاحية لإنشاء تحويلات مالية</h5>
                        <p class="text-muted">تواصل مع مدير النظام للحصول على الصلاحيات المطلوبة</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- التحويلات الأخيرة -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            التحويلات الأخيرة
                        </h5>
                        <a href="transfer_history.php" class="btn btn-sm btn-light">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_transfers)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد تحويلات</h6>
                                <p class="text-muted">لم يتم تنفيذ أي تحويلات بعد</p>
                            </div>
                        <?php else: ?>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($recent_transfers as $transfer): ?>
                                <div class="transfer-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($transfer['sender_name']); ?></h6>
                                            <div class="text-muted small">
                                                إلى: <?php echo htmlspecialchars($transfer['recipient_name']); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                            </div>
                                            <div class="mt-1">
                                                <span class="badge bg-primary">
                                                    <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?><?php echo number_format($transfer['sending_amount'], 2); ?>
                                                    →
                                                    <?php echo htmlspecialchars($transfer['receiving_currency_symbol']); ?><?php echo number_format($transfer['receiving_amount'], 2); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="status-badge status-<?php echo $transfer['status']; ?>">
                                                <?php
                                                $status_labels = [
                                                    'pending' => 'معلق',
                                                    'sent' => 'مرسل',
                                                    'received' => 'مستلم',
                                                    'paid' => 'مدفوع',
                                                    'cancelled' => 'ملغي'
                                                ];
                                                echo $status_labels[$transfer['status']] ?? $transfer['status'];
                                                ?>
                                            </div>
                                            <div class="text-muted small mt-1">
                                                <?php echo date('H:i', strtotime($transfer['created_at'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if ($auth->hasPermission('transfers.edit')): ?>
                                    <div class="mt-2">
                                        <a href="transfer_details.php?id=<?php echo $transfer['id']; ?>" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye me-1"></i> تفاصيل
                                        </a>
                                        <?php if ($transfer['status'] != 'paid' && $transfer['status'] != 'cancelled'): ?>
                                        <button class="btn btn-sm btn-outline-warning" onclick="updateStatus(<?php echo $transfer['id']; ?>)">
                                            <i class="fas fa-edit me-1"></i> تحديث الحالة
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- أهم الممرات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-route me-2"></i>
                            أهم الممرات (30 يوم)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_corridors)): ?>
                            <div class="text-center p-3">
                                <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد بيانات</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($top_corridors as $corridor): ?>
                            <div class="corridor-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($corridor['corridor_name']); ?></div>
                                        <small class="text-muted"><?php echo number_format($corridor['transfer_count']); ?> تحويل</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">$<?php echo number_format($corridor['total_volume'], 0); ?></div>
                                        <small class="text-muted">إجمالي الحجم</small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            إحصائيات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="h4 text-warning mb-0"><?php echo $today_stats['sent_transfers']; ?></div>
                                <small class="text-muted">مرسلة</small>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="h4 text-info mb-0"><?php echo $today_stats['received_transfers']; ?></div>
                                <small class="text-muted">مستلمة</small>
                            </div>
                            <div class="col-12">
                                <div class="progress mb-2" style="height: 8px;">
                                    <?php
                                    $total = $today_stats['today_transfers'];
                                    $completed_percentage = $total > 0 ? ($today_stats['paid_transfers'] / $total) * 100 : 0;
                                    ?>
                                    <div class="progress-bar bg-success" style="width: <?php echo $completed_percentage; ?>%"></div>
                                </div>
                                <small class="text-muted">معدل الإنجاز: <?php echo number_format($completed_percentage, 1); ?>%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغيرات عامة
        let currentStep = 1;
        let selectedSender = null;
        let transferCalculations = null;

        // إدارة الخطوات
        function nextStep(step) {
            if (validateCurrentStep()) {
                // إخفاء الخطوة الحالية
                document.getElementById(`step${currentStep}-content`).style.display = 'none';
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');

                // إظهار الخطوة الجديدة
                currentStep = step;
                document.getElementById(`step${currentStep}-content`).style.display = 'block';
                document.getElementById(`step${currentStep}`).classList.add('active');

                // تحديث ملخص التحويل في الخطوة الأخيرة
                if (step === 4) {
                    updateTransferSummary();
                }
            }
        }

        function prevStep(step) {
            // إخفاء الخطوة الحالية
            document.getElementById(`step${currentStep}-content`).style.display = 'none';
            document.getElementById(`step${currentStep}`).classList.remove('active');

            // إظهار الخطوة السابقة
            currentStep = step;
            document.getElementById(`step${currentStep}-content`).style.display = 'block';
            document.getElementById(`step${currentStep}`).classList.add('active');
            document.getElementById(`step${currentStep}`).classList.remove('completed');
        }

        // التحقق من صحة الخطوة الحالية
        function validateCurrentStep() {
            switch(currentStep) {
                case 1:
                    if (!selectedSender) {
                        alert('يرجى اختيار العميل المرسل');
                        return false;
                    }
                    break;
                case 2:
                    const recipientName = document.querySelector('input[name="recipient_name"]').value;
                    const recipientCountry = document.querySelector('select[name="recipient_country"]').value;
                    if (!recipientName || !recipientCountry) {
                        alert('يرجى إكمال بيانات المستفيد');
                        return false;
                    }
                    break;
                case 3:
                    const sendingCurrency = document.getElementById('sendingCurrency').value;
                    const receivingCurrency = document.getElementById('receivingCurrency').value;
                    const sendingAmount = document.getElementById('sendingAmount').value;
                    const transferMethod = document.querySelector('select[name="transfer_method"]').value;

                    if (!sendingCurrency || !receivingCurrency || !sendingAmount || !transferMethod) {
                        alert('يرجى إكمال جميع تفاصيل التحويل');
                        return false;
                    }

                    if (sendingCurrency === receivingCurrency) {
                        alert('عملة الإرسال والاستلام يجب أن تكونا مختلفتين');
                        return false;
                    }
                    break;
            }
            return true;
        }

        // البحث عن العملاء
        function searchSenders() {
            const searchTerm = document.getElementById('senderSearch').value.trim();

            if (searchTerm.length < 2) {
                document.getElementById('senderResults').innerHTML = '';
                return;
            }

            fetch('search_customers.php?q=' + encodeURIComponent(searchTerm))
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('senderResults');
                resultsDiv.innerHTML = '';

                if (data.length > 0) {
                    data.forEach(customer => {
                        const customerDiv = document.createElement('div');
                        customerDiv.className = 'customer-search-result';
                        customerDiv.innerHTML = `
                            <div class="fw-bold">${customer.full_name}</div>
                            <div class="text-muted small">${customer.id_number} | ${customer.phone || 'لا يوجد هاتف'}</div>
                            <div class="text-muted small">KYC: ${customer.kyc_status}</div>
                        `;
                        customerDiv.onclick = () => selectSender(customer);
                        resultsDiv.appendChild(customerDiv);
                    });
                } else {
                    resultsDiv.innerHTML = '<div class="text-muted text-center p-2">لا توجد نتائج</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // اختيار المرسل
        function selectSender(customer) {
            selectedSender = customer;
            document.getElementById('selectedSenderId').value = customer.id;
            document.getElementById('selectedSenderName').textContent = customer.full_name;
            document.getElementById('senderSearch').value = customer.full_name;
            document.getElementById('senderResults').innerHTML = '';
            document.getElementById('selectedSender').style.display = 'block';
        }

        // تحديث رموز العملات
        function updateCurrencySymbols() {
            const sendingCurrency = document.getElementById('sendingCurrency');

            if (sendingCurrency.value) {
                const sendingOption = sendingCurrency.options[sendingCurrency.selectedIndex];
                document.getElementById('sendingSymbol').textContent = sendingOption.dataset.symbol || '';
            }
        }

        // حساب التحويل
        function calculateTransfer() {
            const sendingCurrencyId = document.getElementById('sendingCurrency').value;
            const receivingCurrencyId = document.getElementById('receivingCurrency').value;
            const sendingAmount = parseFloat(document.getElementById('sendingAmount').value) || 0;
            const recipientCountry = document.querySelector('select[name="recipient_country"]').value;

            if (!sendingCurrencyId || !receivingCurrencyId || sendingAmount <= 0 || !recipientCountry) {
                document.getElementById('transferCalculation').style.display = 'none';
                return;
            }

            // استدعاء API لحساب التحويل
            fetch('calculate_transfer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sending_currency_id: sendingCurrencyId,
                    receiving_currency_id: receivingCurrencyId,
                    sending_amount: sendingAmount,
                    recipient_country: recipientCountry
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    transferCalculations = data;
                    document.getElementById('receivingAmount').textContent = data.receiving_amount.toFixed(2);
                    document.getElementById('exchangeRate').textContent = data.exchange_rate.toFixed(6);
                    document.getElementById('transferFee').textContent = data.transfer_fee.toFixed(2);
                    document.getElementById('totalRequired').textContent = (sendingAmount + data.transfer_fee).toFixed(2);
                    document.getElementById('transferCalculation').style.display = 'block';
                } else {
                    alert('خطأ في حساب التحويل: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في الاتصال بالخادم');
            });
        }

        // تحديث ملخص التحويل
        function updateTransferSummary() {
            const senderName = selectedSender ? selectedSender.full_name : '';
            const recipientName = document.querySelector('input[name="recipient_name"]').value;
            const recipientCountry = document.querySelector('select[name="recipient_country"]').value;
            const sendingCurrency = document.getElementById('sendingCurrency');
            const receivingCurrency = document.getElementById('receivingCurrency');
            const sendingAmount = document.getElementById('sendingAmount').value;
            const transferMethod = document.querySelector('select[name="transfer_method"]');

            const sendingCurrencyText = sendingCurrency.options[sendingCurrency.selectedIndex].text;
            const receivingCurrencyText = receivingCurrency.options[receivingCurrency.selectedIndex].text;
            const transferMethodText = transferMethod.options[transferMethod.selectedIndex].text;

            const summaryHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>المرسل:</strong> ${senderName}<br>
                        <strong>المستفيد:</strong> ${recipientName}<br>
                        <strong>البلد:</strong> ${recipientCountry}
                    </div>
                    <div class="col-md-6">
                        <strong>المبلغ المرسل:</strong> ${sendingAmount} ${sendingCurrencyText}<br>
                        <strong>المبلغ المستلم:</strong> ${transferCalculations ? transferCalculations.receiving_amount.toFixed(2) : '-'} ${receivingCurrencyText}<br>
                        <strong>طريقة التحويل:</strong> ${transferMethodText}
                    </div>
                </div>
                ${transferCalculations ? `
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <strong>سعر الصرف:</strong> ${transferCalculations.exchange_rate.toFixed(6)}<br>
                        <strong>رسوم التحويل:</strong> ${transferCalculations.transfer_fee.toFixed(2)}
                    </div>
                    <div class="col-md-6">
                        <strong>إجمالي المطلوب:</strong> ${(parseFloat(sendingAmount) + transferCalculations.transfer_fee).toFixed(2)}<br>
                        <strong>الربح المتوقع:</strong> ${transferCalculations.total_profit ? transferCalculations.total_profit.toFixed(2) : '-'}
                    </div>
                </div>
                ` : ''}
            `;

            document.getElementById('transferSummary').innerHTML = summaryHTML;
        }

        // تحديث حالة التحويل
        function updateStatus(transferId) {
            // يمكن تطوير هذه الدالة لفتح نافذة منبثقة لتحديث الحالة
            window.location.href = `transfer_details.php?id=${transferId}`;
        }

        // إعداد الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // أحداث البحث عن العملاء
            document.getElementById('senderSearch').addEventListener('input', searchSenders);
            document.getElementById('searchSenderBtn').addEventListener('click', searchSenders);

            // تغيير المرسل
            document.getElementById('changeSender').addEventListener('click', function() {
                selectedSender = null;
                document.getElementById('selectedSenderId').value = '';
                document.getElementById('senderSearch').value = '';
                document.getElementById('selectedSender').style.display = 'none';
            });

            // أحداث العملات
            document.getElementById('sendingCurrency').addEventListener('change', function() {
                updateCurrencySymbols();
                calculateTransfer();
            });

            document.getElementById('receivingCurrency').addEventListener('change', calculateTransfer);
            document.getElementById('sendingAmount').addEventListener('input', calculateTransfer);
            document.querySelector('select[name="recipient_country"]').addEventListener('change', calculateTransfer);

            // التحقق من النموذج قبل الإرسال
            document.getElementById('transferForm').addEventListener('submit', function(e) {
                if (!document.getElementById('confirmTransfer').checked) {
                    e.preventDefault();
                    alert('يرجى تأكيد الموافقة على شروط وأحكام التحويل');
                    return;
                }

                if (!selectedSender) {
                    e.preventDefault();
                    alert('يرجى اختيار العميل المرسل');
                    return;
                }
            });
        });

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>

<?php include '../includes/footer.php'; ?>
