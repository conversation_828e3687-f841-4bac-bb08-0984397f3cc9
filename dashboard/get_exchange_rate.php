<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_manager.php';

header('Content-Type: application/json');

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    echo json_encode(['success' => false, 'message' => 'جلسة غير صحيحة']);
    exit();
}

if (!$auth->hasPermission('exchange.view')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول']);
    exit();
}

// قراءة البيانات من الطلب
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit();
}

$from_currency_id = intval($input['from_currency_id'] ?? 0);
$to_currency_id = intval($input['to_currency_id'] ?? 0);
$amount = floatval($input['amount'] ?? 0);

if (!$from_currency_id || !$to_currency_id || !$amount) {
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit();
}

try {
    $exchangeManager = new ExchangeManager();
    $current_user = $auth->getCurrentUser();
    
    // الحصول على أسعار الصرف
    $rates = $exchangeManager->getCurrentExchangeRates($from_currency_id, $to_currency_id);
    
    if (!$rates) {
        echo json_encode(['success' => false, 'message' => 'أسعار الصرف غير متوفرة']);
        exit();
    }
    
    // إعداد بيانات وهمية للحساب
    $data = [
        'customer_id' => 1, // مؤقت للحساب
        'sell_currency_id' => $from_currency_id,
        'buy_currency_id' => $to_currency_id,
        'sell_amount' => $amount,
        'branch_id' => $current_user['branch_id']
    ];
    
    // حساب المبالغ
    $calculations = $exchangeManager->calculateExchangeAmounts($data, $rates);
    
    echo json_encode([
        'success' => true,
        'buy_amount' => $calculations['buy_amount'],
        'exchange_rate' => $calculations['exchange_rate_used'],
        'commission_amount' => $calculations['commission_amount'],
        'spread_profit' => $calculations['spread_profit'],
        'total_profit' => $calculations['total_profit']
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()]);
}
?>
