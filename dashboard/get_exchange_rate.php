<?php
// منع عرض الأخطاء في JSON output
error_reporting(0);
ini_set('display_errors', 0);

// بدء output buffering وتنظيف أي output سابق
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

try {
    require_once '../includes/auth.php';
    require_once '../includes/exchange_manager.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'خطأ في تحميل الملفات المطلوبة'], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'جلسة غير صحيحة'], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

if (!$auth->hasPermission('exchange.view')) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول'], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

// قراءة البيانات من الطلب
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

$from_currency_id = intval($input['from_currency_id'] ?? 0);
$to_currency_id = intval($input['to_currency_id'] ?? 0);
$amount = floatval($input['amount'] ?? 0);

if (!$from_currency_id || !$to_currency_id || !$amount) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة'], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

try {
    $exchangeManager = new ExchangeManager();
    $current_user = $auth->getCurrentUser();
    
    // الحصول على أسعار الصرف
    $rates = $exchangeManager->getCurrentExchangeRates($from_currency_id, $to_currency_id);

    if (!$rates) {
        ob_clean();
        echo json_encode(['success' => false, 'message' => 'أسعار الصرف غير متوفرة'], JSON_UNESCAPED_UNICODE);
        ob_end_flush();
        exit();
    }
    
    // إعداد بيانات وهمية للحساب
    $data = [
        'customer_id' => 1, // مؤقت للحساب
        'sell_currency_id' => $from_currency_id,
        'buy_currency_id' => $to_currency_id,
        'sell_amount' => $amount,
        'branch_id' => $current_user['branch_id']
    ];
    
    // حساب المبالغ
    $calculations = $exchangeManager->calculateExchangeAmounts($data, $rates);
    
    $response = [
        'success' => true,
        'buy_amount' => $calculations['buy_amount'],
        'exchange_rate' => $calculations['exchange_rate_used'],
        'commission_amount' => $calculations['commission_amount'],
        'spread_profit' => $calculations['spread_profit'],
        'total_profit' => $calculations['total_profit']
    ];

    // تنظيف أي output سابق وإرسال JSON
    ob_clean();
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

// إنهاء output buffering
ob_end_flush();
?>
