<?php
// منع عرض الأخطاء في JSON output
error_reporting(0);
ini_set('display_errors', 0);

// بدء output buffering وتنظيف أي output سابق
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';

header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    ob_clean();
    echo json_encode([], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

if (!$auth->hasPermission('customers.view')) {
    ob_clean();
    echo json_encode([], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

$search_term = trim($_GET['q'] ?? '');

if (strlen($search_term) < 2) {
    ob_clean();
    echo json_encode([], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

try {
    $customerManager = new CustomerManager();

    // البحث عن العملاء النشطين فقط
    $filters = ['blacklisted' => false];
    $customers = $customerManager->searchCustomers($search_term, $filters, 10, 0);

    // تنسيق النتائج
    $results = [];
    foreach ($customers as $customer) {
        $results[] = [
            'id' => $customer['id'],
            'full_name' => $customer['full_name'],
            'id_number' => $customer['id_number'],
            'phone' => $customer['phone'],
            'email' => $customer['email'],
            'kyc_status' => $customer['kyc_status'],
            'risk_level' => $customer['risk_level']
        ];
    }

    ob_clean();
    echo json_encode($results, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    ob_clean();
    echo json_encode([], JSON_UNESCAPED_UNICODE);
}

// إنهاء output buffering
ob_end_flush();
?>
