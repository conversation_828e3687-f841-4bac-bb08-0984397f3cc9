<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_manager.php';

$auth = new Auth();
$exchangeManager = new ExchangeManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.create')) {
    header('Location: exchange.php?error=ليس لديك صلاحية لإنشاء عمليات صرافة');
    exit();
}

$current_user = $auth->getCurrentUser();

// التحقق من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: exchange.php?error=طريقة طلب غير صحيحة');
    exit();
}

// جمع البيانات من النموذج
$data = [
    'customer_id' => intval($_POST['customer_id'] ?? 0),
    'sell_currency_id' => intval($_POST['sell_currency_id'] ?? 0),
    'buy_currency_id' => intval($_POST['buy_currency_id'] ?? 0),
    'sell_amount' => floatval($_POST['sell_amount'] ?? 0),
    'buy_amount' => floatval($_POST['buy_amount'] ?? 0),
    'branch_id' => intval($_POST['branch_id'] ?? $current_user['branch_id'])
];

// التحقق من صحة البيانات الأساسية
if (empty($data['customer_id']) || empty($data['sell_currency_id']) || 
    empty($data['buy_currency_id']) || empty($data['sell_amount'])) {
    header('Location: exchange.php?error=بيانات غير مكتملة');
    exit();
}

// معالجة عملية الصرافة
$result = $exchangeManager->createExchangeTransaction($data, $current_user['id']);

if ($result['success']) {
    // إعادة توجيه إلى صفحة الإيصال
    header('Location: exchange_receipt.php?id=' . $result['transaction_id'] . '&success=' . urlencode($result['message']));
} else {
    // إعادة توجيه مع رسالة خطأ
    header('Location: exchange.php?error=' . urlencode($result['message']));
}
exit();
?>
