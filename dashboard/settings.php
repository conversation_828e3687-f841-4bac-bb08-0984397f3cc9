<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('settings.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب العملات
    $stmt = $db->prepare("SELECT * FROM currencies ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $currencies = $stmt->fetchAll();
    
    // جلب إعدادات النظام
    $stmt = $db->prepare("SELECT * FROM settings");
    $stmt->execute();
    $settings_array = $stmt->fetchAll();
    $settings = [];
    foreach ($settings_array as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
} catch (Exception $e) {
    $error_message = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// معالجة إضافة عملة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_currency') {
    if ($auth->hasPermission('settings.currencies')) {
        $name = trim($_POST['currency_name'] ?? '');
        $code = strtoupper(trim($_POST['currency_code'] ?? ''));
        $symbol = trim($_POST['currency_symbol'] ?? '');
        $decimal_places = intval($_POST['decimal_places'] ?? 2);
        
        if (empty($name) || empty($code) || empty($symbol)) {
            $error_message = 'يرجى ملء جميع حقول العملة';
        } else {
            try {
                $stmt = $db->prepare("INSERT INTO currencies (name, code, symbol, decimal_places) VALUES (:name, :code, :symbol, :decimal_places)");
                $stmt->bindParam(':name', $name);
                $stmt->bindParam(':code', $code);
                $stmt->bindParam(':symbol', $symbol);
                $stmt->bindParam(':decimal_places', $decimal_places);
                $stmt->execute();
                
                $success_message = 'تم إضافة العملة بنجاح';
                
                // إعادة جلب العملات
                $stmt = $db->prepare("SELECT * FROM currencies ORDER BY is_base_currency DESC, name ASC");
                $stmt->execute();
                $currencies = $stmt->fetchAll();
                
            } catch (Exception $e) {
                $error_message = 'خطأ في إضافة العملة: ' . $e->getMessage();
            }
        }
    } else {
        $error_message = 'ليس لديك صلاحية لإضافة العملات';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        
        .badge-base {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">الإعدادات</h2>
                        <p class="text-muted">إدارة إعدادات النظام والعملات</p>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- إعدادات الشركة -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>اسم الشركة:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['company_name'] ?? 'غير محدد'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>العنوان:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['company_address'] ?? 'غير محدد'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>الهاتف:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['company_phone'] ?? 'غير محدد'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>البريد الإلكتروني:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['company_email'] ?? 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>العملة الأساسية:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['base_currency'] ?? 'USD'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>بداية السنة المالية:</strong></label>
                            <p class="form-control-plaintext"><?php echo htmlspecialchars($settings['financial_year_start'] ?? '01-01'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>الحد الأقصى للصرافة اليومية:</strong></label>
                            <p class="form-control-plaintext">$<?php echo number_format($settings['max_daily_exchange_limit'] ?? 100000); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>مبلغ التحقق من الهوية:</strong></label>
                            <p class="form-control-plaintext">$<?php echo number_format($settings['kyc_required_amount'] ?? 10000); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إدارة العملات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-coins me-2"></i>
                    إدارة العملات
                </h5>
                <?php if ($auth->hasPermission('settings.currencies')): ?>
                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عملة جديدة
                </button>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم العملة</th>
                                <th>الرمز</th>
                                <th>الرمز المختصر</th>
                                <th>المنازل العشرية</th>
                                <th>الحالة</th>
                                <th>النوع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($currencies as $currency): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($currency['name']); ?></td>
                                <td><?php echo htmlspecialchars($currency['symbol']); ?></td>
                                <td><code><?php echo htmlspecialchars($currency['code']); ?></code></td>
                                <td><?php echo $currency['decimal_places']; ?></td>
                                <td>
                                    <?php if ($currency['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($currency['is_base_currency']): ?>
                                        <span class="badge badge-base">العملة الأساسية</span>
                                    <?php else: ?>
                                        <span class="badge bg-light text-dark">عملة فرعية</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة عملة جديدة -->
    <?php if ($auth->hasPermission('settings.currencies')): ?>
    <div class="modal fade" id="addCurrencyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة عملة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add_currency">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="currency_name" class="form-label">اسم العملة</label>
                            <input type="text" class="form-control" id="currency_name" name="currency_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="currency_code" class="form-label">الرمز المختصر (3 أحرف)</label>
                            <input type="text" class="form-control" id="currency_code" name="currency_code" maxlength="3" required>
                        </div>
                        <div class="mb-3">
                            <label for="currency_symbol" class="form-label">رمز العملة</label>
                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" required>
                        </div>
                        <div class="mb-3">
                            <label for="decimal_places" class="form-label">عدد المنازل العشرية</label>
                            <select class="form-control" id="decimal_places" name="decimal_places">
                                <option value="0">0</option>
                                <option value="2" selected>2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة العملة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
