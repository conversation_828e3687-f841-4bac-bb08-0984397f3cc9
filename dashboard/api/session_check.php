<?php
/**
 * Trust Plus - Session Check API
 * API فحص الجلسة
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 0); // إخفاء الأخطاء في JSON

try {
    require_once '../../config.php';
    require_once '../../includes/database.php';
    require_once '../../includes/auth.php';
    
    // بدء الجلسة إذا لم تكن نشطة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $auth = new Auth();
    $action = $_GET['action'] ?? $_POST['action'] ?? 'check';
    
    switch ($action) {
        case 'check':
        case 'check_session':
            // فحص صحة الجلسة
            $isValid = $auth->checkSession();
            
            if ($isValid) {
                $user = $auth->getCurrentUser();
                echo json_encode([
                    'success' => true,
                    'valid' => true,
                    'user' => [
                        'id' => $user['id'] ?? null,
                        'username' => $user['username'] ?? null,
                        'full_name' => $user['full_name'] ?? null,
                        'role_id' => $user['role_id'] ?? null
                    ],
                    'session_info' => [
                        'login_time' => $_SESSION['login_time'] ?? null,
                        'last_activity' => $_SESSION['last_activity'] ?? time(),
                        'session_timeout' => defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600
                    ],
                    'timestamp' => time(),
                    'message' => 'الجلسة صالحة'
                ], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'valid' => false,
                    'message' => 'الجلسة غير صالحة أو منتهية الصلاحية',
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'renew':
        case 'renew_session':
            // تجديد الجلسة
            if (!$auth->checkSession()) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => 'الجلسة غير صالحة',
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            // تحديث وقت آخر نشاط
            $_SESSION['last_activity'] = time();
            
            // تسجيل النشاط
            $user = $auth->getCurrentUser();
            if ($user) {
                // يمكن إضافة تسجيل النشاط هنا إذا لزم الأمر
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تجديد الجلسة بنجاح',
                'session_info' => [
                    'last_activity' => $_SESSION['last_activity'],
                    'session_timeout' => defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600
                ],
                'timestamp' => time()
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'heartbeat':
            // نبضة للحفاظ على الجلسة نشطة
            if ($auth->checkSession()) {
                $_SESSION['last_activity'] = time();
                echo json_encode([
                    'success' => true,
                    'valid' => true,
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'valid' => false,
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'info':
            // معلومات الجلسة
            if (!$auth->checkSession()) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => 'الجلسة غير صالحة',
                    'timestamp' => time()
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            $user = $auth->getCurrentUser();
            $sessionTimeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600;
            $loginTime = $_SESSION['login_time'] ?? time();
            $lastActivity = $_SESSION['last_activity'] ?? time();
            $timeRemaining = $sessionTimeout - (time() - $lastActivity);
            
            echo json_encode([
                'success' => true,
                'session_info' => [
                    'user_id' => $user['id'] ?? null,
                    'username' => $user['username'] ?? null,
                    'full_name' => $user['full_name'] ?? null,
                    'login_time' => $loginTime,
                    'last_activity' => $lastActivity,
                    'session_timeout' => $sessionTimeout,
                    'time_remaining' => max(0, $timeRemaining),
                    'is_active' => $timeRemaining > 0
                ],
                'timestamp' => time()
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'إجراء غير صالح',
                'available_actions' => ['check', 'renew', 'heartbeat', 'info'],
                'timestamp' => time()
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_code' => 'SESSION_API_ERROR',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}
?>
