<?php
/**
 * Trust Plus - Session Check API
 * API للتحقق من حالة الجلسة
 */

// تفعيل output buffering لتجنب مشاكل JSON
ob_start();

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// منع التخزين المؤقت
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

try {
    require_once '../../config.php';
    require_once '../../includes/auth.php';
    
    $auth = new Auth();
    
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? $_GET['action'] ?? 'check_session';
    
    switch ($action) {
        case 'check_session':
            handleCheckSession($auth);
            break;
            
        case 'extend_session':
            handleExtendSession($auth);
            break;
            
        case 'get_session_info':
            handleGetSessionInfo($auth);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'SYSTEM_ERROR'
    ], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
    exit();
}

/**
 * التحقق من حالة الجلسة
 */
function handleCheckSession($auth) {
    $session_check = $auth->checkSessionWithReason();
    
    if ($session_check['valid']) {
        $current_user = $auth->getCurrentUser();
        $session_info = getSessionInfo();
        
        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'الجلسة صحيحة',
            'data' => [
                'user_id' => $current_user['id'],
                'username' => $current_user['username'],
                'full_name' => $current_user['full_name'],
                'session_info' => $session_info
            ]
        ], JSON_UNESCAPED_UNICODE);
        ob_end_flush();
        
    } else {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => $session_check['message'],
            'reason' => $session_check['reason'],
            'error_code' => 'SESSION_INVALID'
        ], JSON_UNESCAPED_UNICODE);
        ob_end_flush();
    }
}

/**
 * تمديد الجلسة
 */
function handleExtendSession($auth) {
    $session_check = $auth->checkSessionWithReason();
    
    if (!$session_check['valid']) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'لا يمكن تمديد جلسة منتهية الصلاحية',
            'reason' => $session_check['reason'],
            'error_code' => 'SESSION_EXPIRED'
        ], JSON_UNESCAPED_UNICODE);
        ob_end_flush();
        return;
    }
    
    // تحديث وقت آخر نشاط
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['last_activity'] = time();
    $_SESSION['extended_at'] = time();
    $_SESSION['extension_count'] = ($_SESSION['extension_count'] ?? 0) + 1;
    
    // تسجيل عملية التمديد
    $current_user = $auth->getCurrentUser();
    if ($current_user) {
        $auth->logActivity(
            $current_user['id'], 
            'session_extended', 
            'session', 
            session_id(),
            'تم تمديد الجلسة - العدد: ' . $_SESSION['extension_count']
        );
    }
    
    $session_info = getSessionInfo();
    
    ob_clean();
    echo json_encode([
        'success' => true,
        'message' => 'تم تمديد الجلسة بنجاح',
        'data' => [
            'extended_at' => date('Y-m-d H:i:s'),
            'extension_count' => $_SESSION['extension_count'],
            'session_info' => $session_info
        ]
    ], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
}

/**
 * الحصول على معلومات الجلسة
 */
function handleGetSessionInfo($auth) {
    $session_check = $auth->checkSessionWithReason();
    
    if (!$session_check['valid']) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => $session_check['message'],
            'reason' => $session_check['reason'],
            'error_code' => 'SESSION_INVALID'
        ], JSON_UNESCAPED_UNICODE);
        ob_end_flush();
        return;
    }
    
    $current_user = $auth->getCurrentUser();
    $session_info = getSessionInfo();
    
    ob_clean();
    echo json_encode([
        'success' => true,
        'message' => 'معلومات الجلسة',
        'data' => [
            'user' => [
                'id' => $current_user['id'],
                'username' => $current_user['username'],
                'full_name' => $current_user['full_name'],
                'email' => $current_user['email'],
                'role_id' => $current_user['role_id']
            ],
            'session' => $session_info
        ]
    ], JSON_UNESCAPED_UNICODE);
    ob_end_flush();
}

/**
 * الحصول على معلومات الجلسة الحالية
 */
function getSessionInfo() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $current_time = time();
    $login_time = $_SESSION['login_time'] ?? $current_time;
    $last_activity = $_SESSION['last_activity'] ?? $login_time;
    
    $session_timeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600;
    $max_session_time = defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME : 86400;
    
    $time_since_login = $current_time - $login_time;
    $time_since_activity = $current_time - $last_activity;
    $time_until_timeout = $session_timeout - $time_since_activity;
    $time_until_max_expiry = $max_session_time - $time_since_login;
    
    return [
        'session_id' => session_id(),
        'login_time' => date('Y-m-d H:i:s', $login_time),
        'last_activity' => date('Y-m-d H:i:s', $last_activity),
        'current_time' => date('Y-m-d H:i:s', $current_time),
        'time_since_login' => $time_since_login,
        'time_since_activity' => $time_since_activity,
        'time_until_timeout' => max(0, $time_until_timeout),
        'time_until_max_expiry' => max(0, $time_until_max_expiry),
        'session_timeout' => $session_timeout,
        'max_session_time' => $max_session_time,
        'extension_count' => $_SESSION['extension_count'] ?? 0,
        'extended_at' => isset($_SESSION['extended_at']) ? date('Y-m-d H:i:s', $_SESSION['extended_at']) : null,
        'is_near_expiry' => $time_until_timeout <= 300, // أقل من 5 دقائق
        'expiry_percentage' => min(100, ($time_since_activity / $session_timeout) * 100)
    ];
}
?>
