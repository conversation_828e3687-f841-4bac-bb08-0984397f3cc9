<?php
require_once '../config.php';
require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'لوحة التحكم - ' . SYSTEM_NAME;
$page_header = 'لوحة التحكم';
$page_subtitle = 'مرحباً بك في نظام Trust Plus';
$page_icon = 'fas fa-tachometer-alt';
$show_breadcrumb = false;
?>
<?php include '../includes/header.php'; ?>

<!-- محتوى لوحة التحكم -->
<div class="container-fluid">
    <!-- رأس لوحة التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-header">
                <h2 class="dashboard-welcome mb-0">مرحباً، <?php echo htmlspecialchars($current_user['full_name']); ?></h2>
                <p class="dashboard-subtitle">نظرة عامة على أنشطة اليوم</p>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" data-stat="total_customers">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary-gradient">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value">0</div>
                        <div class="stat-label">إجمالي العملاء</div>
                        <div class="stat-change">+0.0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" data-stat="today_exchanges">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success-gradient">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value">0</div>
                        <div class="stat-label">عمليات الصرافة اليوم</div>
                        <div class="stat-change">+0.0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" data-stat="pending_transfers">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning-gradient">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value">0</div>
                        <div class="stat-label">التحويلات المعلقة</div>
                        <div class="stat-change">+0.0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" data-stat="today_revenue">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info-gradient">
                        <i class="fas fa-shekel-sign"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stat-value">₪0</div>
                        <div class="stat-label">إجمالي الإيرادات اليوم</div>
                        <div class="stat-change">+0.0%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row">
        <div class="col-12">
            <div class="quick-actions">
                <h5 class="mb-3">
                    <i class="fas fa-bolt text-primary me-2"></i>
                    الإجراءات السريعة
                </h5>
                <div class="action-grid">
                    <?php if ($auth->hasPermission('customers.create')): ?>
                    <a href="customers.php?action=add" class="action-btn">
                        <i class="fas fa-user-plus text-primary"></i>
                        <span>إضافة عميل جديد</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('exchange.create')): ?>
                    <a href="exchange.php" class="action-btn">
                        <i class="fas fa-exchange-alt text-success"></i>
                        <span>عملية صرافة جديدة</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('exchange.rates')): ?>
                    <a href="exchange_rates.php" class="action-btn">
                        <i class="fas fa-chart-line text-info"></i>
                        <span>تحديث أسعار الصرف</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('transfers.create')): ?>
                    <a href="transfers.php?action=add" class="action-btn">
                        <i class="fas fa-paper-plane text-warning"></i>
                        <span>تحويل مالي جديد</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('reports.financial')): ?>
                    <a href="financial_dashboard.php" class="action-btn">
                        <i class="fas fa-tachometer-alt text-info"></i>
                        <span>لوحة الأداء المالي</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('reports.financial')): ?>
                    <a href="reports.php" class="action-btn">
                        <i class="fas fa-file-chart-line text-secondary"></i>
                        <span>التقارير المالية</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية والأنشطة -->
    <div class="row mt-4">
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <div class="chart-header">
                    <h5 class="chart-title">الإيرادات الشهرية</h5>
                </div>
                <div class="chart-canvas" id="revenueChart"></div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="recent-activities">
                <h5 class="mb-3">
                    <i class="fas fa-clock text-primary me-2"></i>
                    الأنشطة الأخيرة
                </h5>
                <div class="activities-list">
                    <div class="activity-item">
                        <div class="activity-icon bg-success-gradient">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">عملية صرافة جديدة</div>
                            <div class="activity-time">منذ 5 دقائق</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-primary-gradient">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">عميل جديد مسجل</div>
                            <div class="activity-time">منذ 15 دقيقة</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-warning-gradient">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">تحويل مالي معلق</div>
                            <div class="activity-time">منذ 30 دقيقة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
