<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

$current_user = $auth->getCurrentUser();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 250px;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-right: 70px;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .bg-primary-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .bg-success-gradient {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .bg-warning-gradient {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .bg-info-gradient {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="p-3 text-center border-bottom border-light">
            <h5 class="text-white mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="sidebar-text">Trust Plus</span>
            </h5>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>
            
            <?php if ($auth->hasPermission('customers.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span class="sidebar-text">إدارة العملاء</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('exchange.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="exchange.php">
                    <i class="fas fa-exchange-alt"></i>
                    <span class="sidebar-text">عمليات الصرافة</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('transfers.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="transfers.php">
                    <i class="fas fa-paper-plane"></i>
                    <span class="sidebar-text">التحويلات المالية</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('transactions.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="transactions.php">
                    <i class="fas fa-receipt"></i>
                    <span class="sidebar-text">المعاملات المالية</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('cash.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="cash.php">
                    <i class="fas fa-cash-register"></i>
                    <span class="sidebar-text">الصناديق والبنوك</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('reports.financial')): ?>
            <li class="nav-item">
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('settings.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">الإعدادات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('users.view')): ?>
            <li class="nav-item">
                <a class="nav-link" href="users.php">
                    <i class="fas fa-user-cog"></i>
                    <span class="sidebar-text">إدارة المستخدمين</span>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <?php echo htmlspecialchars($current_user['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="fas fa-key me-2"></i>تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Dashboard Content -->
        <div class="container-fluid p-4">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-0">مرحباً، <?php echo htmlspecialchars($current_user['full_name']); ?></h2>
                    <p class="text-muted">نظرة عامة على أنشطة اليوم</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-gradient me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">إجمالي العملاء</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-gradient me-3">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">عمليات الصرافة اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-gradient me-3">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">التحويلات المعلقة</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info-gradient me-3">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">$0</h3>
                                <p class="text-muted mb-0">إجمالي الإيرادات اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="stats-card">
                        <h5 class="mb-3">الإجراءات السريعة</h5>
                        <div class="row">
                            <?php if ($auth->hasPermission('customers.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="customers.php?action=add" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus mb-2 d-block"></i>
                                    إضافة عميل جديد
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('exchange.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="exchange.php?action=add" class="btn btn-outline-success w-100">
                                    <i class="fas fa-exchange-alt mb-2 d-block"></i>
                                    عملية صرافة جديدة
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('transfers.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="transfers.php?action=add" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-paper-plane mb-2 d-block"></i>
                                    تحويل مالي جديد
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('reports.financial')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="reports.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-chart-bar mb-2 d-block"></i>
                                    عرض التقارير
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle Sidebar
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        });
    </script>
</body>
</html>
