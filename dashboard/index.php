<?php
require_once '../config.php';
require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'لوحة التحكم - ' . SYSTEM_NAME;
$page_header = 'لوحة التحكم';
$page_subtitle = 'مرحباً بك في نظام Trust Plus';
$page_icon = 'fas fa-tachometer-alt';
$show_breadcrumb = false;
?>
<?php include '../includes/header.php'; ?>
<!-- محتوى لوحة التحكم -->
<div class="dashboard-container">
    <div class="container-fluid p-4">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-0">مرحباً، <?php echo htmlspecialchars($current_user['full_name']); ?></h2>
                    <p class="text-muted">نظرة عامة على أنشطة اليوم</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-gradient me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">إجمالي العملاء</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-gradient me-3">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">عمليات الصرافة اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-gradient me-3">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">0</h3>
                                <p class="text-muted mb-0">التحويلات المعلقة</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info-gradient me-3">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">$0</h3>
                                <p class="text-muted mb-0">إجمالي الإيرادات اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="stats-card">
                        <h5 class="mb-3">الإجراءات السريعة</h5>
                        <div class="row">
                            <?php if ($auth->hasPermission('customers.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="customers.php?action=add" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus mb-2 d-block"></i>
                                    إضافة عميل جديد
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('exchange.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="exchange.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-exchange-alt mb-2 d-block"></i>
                                    عملية صرافة جديدة
                                </a>
                            </div>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('exchange.rates')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="exchange_rates.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-chart-line mb-2 d-block"></i>
                                    تحديث أسعار الصرف
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('transfers.create')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="transfers.php?action=add" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-paper-plane mb-2 d-block"></i>
                                    تحويل مالي جديد
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($auth->hasPermission('reports.financial')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="financial_dashboard.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-tachometer-alt mb-2 d-block"></i>
                                    لوحة الأداء المالي
                                </a>
                            </div>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('reports.financial')): ?>
                            <div class="col-md-3 mb-3">
                                <a href="reports.php" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-chart-line mb-2 d-block"></i>
                                    التقارير المالية
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
