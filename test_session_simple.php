<?php
/**
 * Trust Plus - Simple Session Test
 * اختبار مبسط لنظام الجلسات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config.php';
    require_once 'includes/auth.php';
    
    $auth = new Auth();
    $session_valid = $auth->checkSession();
    
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار الجلسة - Trust Plus</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }
            .container {
                background: white;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                padding: 30px;
                margin: 20px auto;
                max-width: 800px;
            }
            .status-card {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 15px 0;
                border-left: 4px solid #007bff;
            }
            .btn-test {
                margin: 5px;
                min-width: 200px;
            }
            .session-info {
                background: #e3f2fd;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
            }
        </style>
    </head>
    <body data-session-monitor="true" class="logged-in">
        <div class="container">
            <div class="text-center mb-4">
                <h1><i class="fas fa-shield-alt text-primary me-2"></i>اختبار نظام الجلسات</h1>
                <p class="text-muted">Trust Plus Session Management Test</p>
            </div>
            
            <?php if ($session_valid): ?>
                <div class="status-card">
                    <h3><i class="fas fa-check-circle text-success me-2"></i>الجلسة نشطة</h3>
                    <p>أنت مسجل دخول بنجاح ومراقب الجلسة يعمل.</p>
                    
                    <?php 
                    $current_user = $auth->getCurrentUser();
                    if ($current_user): ?>
                        <div class="session-info">
                            <strong>المستخدم:</strong> <?php echo htmlspecialchars($current_user['full_name']); ?><br>
                            <strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($current_user['username']); ?><br>
                            <strong>وقت تسجيل الدخول:</strong> <?php echo isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'غير محدد'; ?><br>
                            <strong>آخر نشاط:</strong> <?php echo isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'غير محدد'; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="text-center">
                    <h4>اختبار وظائف الجلسة</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <button onclick="testSessionCheck()" class="btn btn-primary btn-test">
                                <i class="fas fa-search me-2"></i>فحص الجلسة
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button onclick="testExtendSession()" class="btn btn-success btn-test">
                                <i class="fas fa-clock me-2"></i>تمديد الجلسة
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button onclick="showTestWarning()" class="btn btn-warning btn-test">
                                <i class="fas fa-exclamation-triangle me-2"></i>اختبار التحذير
                            </button>
                        </div>
                        <div class="col-md-6">
                            <a href="auth/logout.php" class="btn btn-danger btn-test">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
                
            <?php else: ?>
                <div class="status-card">
                    <h3><i class="fas fa-times-circle text-danger me-2"></i>لا توجد جلسة نشطة</h3>
                    <p>يجب تسجيل الدخول أولاً لاختبار نظام الجلسات.</p>
                    <div class="text-center">
                        <a href="auth/login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="mt-4">
                <h4>معلومات النظام</h4>
                <div class="session-info">
                    <strong>مهلة انتهاء الجلسة:</strong> <?php echo defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600; ?> ثانية<br>
                    <strong>الحد الأقصى للجلسة:</strong> <?php echo defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME : 86400; ?> ثانية<br>
                    <strong>وقت التحذير:</strong> <?php echo defined('SESSION_WARNING_TIME') ? SESSION_WARNING_TIME : 300; ?> ثانية<br>
                    <strong>معرف الجلسة:</strong> <?php echo session_id(); ?>
                </div>
            </div>
            
            <div id="testResults" class="mt-4"></div>
        </div>
        
        <!-- تضمين Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        
        <!-- تضمين مراقب الجلسة -->
        <script src="assets/js/session-monitor-simple.js"></script>
        
        <script>
            // دوال الاختبار
            async function testSessionCheck() {
                showResult('جاري فحص الجلسة...', 'info');
                
                try {
                    const response = await fetch('dashboard/api/check_session.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'check_session'
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showResult('✅ الجلسة صحيحة: ' + result.message, 'success');
                    } else {
                        showResult('❌ مشكلة في الجلسة: ' + result.message, 'danger');
                    }
                    
                } catch (error) {
                    showResult('❌ خطأ في فحص الجلسة: ' + error.message, 'danger');
                }
            }
            
            async function testExtendSession() {
                showResult('جاري تمديد الجلسة...', 'info');
                
                try {
                    const response = await fetch('dashboard/api/check_session.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'extend_session'
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showResult('✅ تم تمديد الجلسة بنجاح', 'success');
                    } else {
                        showResult('❌ فشل في تمديد الجلسة: ' + result.message, 'danger');
                    }
                    
                } catch (error) {
                    showResult('❌ خطأ في تمديد الجلسة: ' + error.message, 'danger');
                }
            }
            
            function showTestWarning() {
                if (typeof window.sessionMonitor !== 'undefined') {
                    // محاكاة تحذير انتهاء الجلسة
                    showResult('عرض تحذير اختبار...', 'warning');
                    
                    // إنشاء نافذة تحذير مؤقتة
                    const modal = document.getElementById('sessionWarningModal');
                    if (modal) {
                        const countdown = document.getElementById('sessionCountdown');
                        if (countdown) {
                            countdown.textContent = '0:30';
                        }
                        
                        modal.style.display = 'block';
                        modal.classList.add('show');
                        document.body.classList.add('modal-open');
                        
                        // إضافة backdrop
                        const backdrop = document.createElement('div');
                        backdrop.className = 'modal-backdrop fade show';
                        document.body.appendChild(backdrop);
                        
                        showResult('✅ تم عرض نافذة التحذير الاختبارية', 'success');
                    } else {
                        showResult('❌ لم يتم العثور على نافذة التحذير', 'danger');
                    }
                } else {
                    showResult('❌ مراقب الجلسة غير متوفر', 'danger');
                }
            }
            
            function showResult(message, type) {
                const resultsDiv = document.getElementById('testResults');
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                resultsDiv.appendChild(alertDiv);
                
                // إزالة الرسالة بعد 5 ثوانٍ
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
            
            // عرض معلومات مراقب الجلسة عند التحميل
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    if (typeof window.sessionMonitor !== 'undefined') {
                        showResult('✅ تم تحميل مراقب الجلسة بنجاح', 'success');
                    } else {
                        showResult('⚠️ مراقب الجلسة غير متوفر', 'warning');
                    }
                }, 2000);
            });
        </script>
    </body>
    </html>
    
    <?php
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في اختبار الجلسة</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
