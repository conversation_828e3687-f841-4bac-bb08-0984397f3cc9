<?php
/**
 * Trust Plus - Test Homepage
 * اختبار الصفحة الرئيسية
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏠 اختبار الصفحة الرئيسية</h1>";
echo "<hr>";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>🔍 فحص الملفات الأساسية</h2>";

$files_to_check = [
    'config.php' => 'ملف الإعدادات',
    'includes/auth.php' => 'ملف المصادقة',
    'includes/database.php' => 'ملف قاعدة البيانات',
    'includes/header.php' => 'ملف الرأس',
    'includes/footer.php' => 'ملف التذييل',
    'includes/sidebar.php' => 'ملف الشريط الجانبي',
    'includes/navbar.php' => 'ملف شريط التنقل',
    'includes/assets.php' => 'ملف إدارة الأصول',
    'dashboard/index.php' => 'لوحة التحكم',
    'auth/login.php' => 'صفحة تسجيل الدخول'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>الملف</th>";
echo "<th style='padding: 10px;'>الوصف</th>";
echo "<th style='padding: 10px;'>الحالة</th>";
echo "</tr>";

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? '✅ موجود' : '❌ مفقود';
    $color = $exists ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>$file</td>";
    echo "<td style='padding: 8px;'>$description</td>";
    echo "<td style='padding: 8px; color: $color;'>$status</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔍 فحص مجلدات الأصول</h2>";

$asset_folders = [
    'assets/' => 'مجلد الأصول الرئيسي',
    'assets/css/' => 'ملفات CSS',
    'assets/js/' => 'ملفات JavaScript',
    'assets/images/' => 'الصور',
    'assets/fonts/' => 'الخطوط'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>المجلد</th>";
echo "<th style='padding: 10px;'>الوصف</th>";
echo "<th style='padding: 10px;'>الحالة</th>";
echo "<th style='padding: 10px;'>عدد الملفات</th>";
echo "</tr>";

foreach ($asset_folders as $folder => $description) {
    $exists = is_dir($folder);
    $status = $exists ? '✅ موجود' : '❌ مفقود';
    $color = $exists ? 'green' : 'red';
    $file_count = $exists ? count(glob($folder . '*')) : 0;
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>$folder</td>";
    echo "<td style='padding: 8px;'>$description</td>";
    echo "<td style='padding: 8px; color: $color;'>$status</td>";
    echo "<td style='padding: 8px; text-align: center;'>$file_count</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔍 اختبار قاعدة البيانات</h2>";

try {
    require_once 'config.php';
    require_once 'includes/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // اختبار الجداول الأساسية
    $tables_to_check = [
        'users' => 'المستخدمين',
        'roles' => 'الأدوار',
        'permissions' => 'الصلاحيات',
        'customers' => 'العملاء',
        'currencies' => 'العملات',
        'exchange_rates' => 'أسعار الصرف'
    ];
    
    echo "<h3>📊 فحص الجداول</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>الجدول</th>";
    echo "<th style='padding: 10px;'>الوصف</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "<th style='padding: 10px;'>عدد السجلات</th>";
    echo "</tr>";
    
    foreach ($tables_to_check as $table => $description) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>$table</td>";
            echo "<td style='padding: 8px;'>$description</td>";
            echo "<td style='padding: 8px; color: green;'>✅ موجود</td>";
            echo "<td style='padding: 8px; text-align: center;'>$count</td>";
            echo "</tr>";
            
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>$table</td>";
            echo "<td style='padding: 8px;'>$description</td>";
            echo "<td style='padding: 8px; color: red;'>❌ مفقود</td>";
            echo "<td style='padding: 8px; text-align: center;'>-</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🔍 اختبار المصادقة</h2>";

try {
    require_once 'includes/auth.php';
    
    $auth = new Auth();
    echo "<p style='color: green;'>✅ تم تحميل فئة المصادقة بنجاح</p>";
    
    // فحص الجلسة
    $session_valid = $auth->checkSession();
    if ($session_valid) {
        $current_user = $auth->getCurrentUser();
        echo "<p style='color: green;'>✅ المستخدم مسجل دخول: " . htmlspecialchars($current_user['full_name']) . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا يوجد مستخدم مسجل دخول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في المصادقة: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🔍 اختبار تحميل الصفحات</h2>";

$pages_to_test = [
    'index.php' => 'الصفحة الرئيسية',
    'dashboard/index.php' => 'لوحة التحكم',
    'auth/login.php' => 'تسجيل الدخول'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>الصفحة</th>";
echo "<th style='padding: 10px;'>الوصف</th>";
echo "<th style='padding: 10px;'>الرابط</th>";
echo "</tr>";

foreach ($pages_to_test as $page => $description) {
    $url = "http://localhost/Trust%20Plus/$page";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>$page</td>";
    echo "<td style='padding: 8px;'>$description</td>";
    echo "<td style='padding: 8px;'>";
    echo "<a href='$url' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>اختبار</a>";
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔧 حلول المشاكل المحتملة</h2>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #ffeaa7;'>";
echo "<h3>💡 إذا كانت الصفحة لا تعمل:</h3>";
echo "<ol>";
echo "<li><strong>تأكد من تشغيل XAMPP:</strong> Apache و MySQL يجب أن يكونا نشطين</li>";
echo "<li><strong>تحقق من قاعدة البيانات:</strong> تأكد من وجود قاعدة البيانات trust_plus</li>";
echo "<li><strong>فحص الأصول:</strong> تأكد من وجود مجلد assets وملفاته</li>";
echo "<li><strong>مسح الكاش:</strong> امسح كاش المتصفح وأعد تحميل الصفحة</li>";
echo "<li><strong>فحص الأخطاء:</strong> تحقق من سجل أخطاء Apache</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h3>🚀 روابط سريعة للاختبار:</h3>";
echo "<p><a href='auth/login.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a></p>";
echo "<p><a href='dashboard/index.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 لوحة التحكم</a></p>";
echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 الصرافة</a></p>";
echo "</div>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    font-size: 0.9em;
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ol {
    line-height: 1.6;
}
</style>
