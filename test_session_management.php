<?php
/**
 * Trust Plus - Session Management Test
 * اختبار نظام إدارة الجلسات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔐 اختبار نظام إدارة الجلسات</h1>";
echo "<hr>";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config.php';
    require_once 'includes/auth.php';
    
    $auth = new Auth();
    
    echo "<h2>🔍 فحص حالة الجلسة الحالية</h2>";
    
    $session_check = $auth->checkSessionWithReason();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>المعلومة</th>";
    echo "<th style='padding: 10px;'>القيمة</th>";
    echo "</tr>";
    
    $session_info = [
        'حالة الجلسة' => $session_check['valid'] ? '✅ صحيحة' : '❌ غير صحيحة',
        'سبب الفشل' => $session_check['reason'] ?? 'لا يوجد',
        'الرسالة' => $session_check['message'],
        'معرف الجلسة' => session_id(),
        'حالة PHP Session' => session_status() == PHP_SESSION_ACTIVE ? 'نشطة' : 'غير نشطة'
    ];
    
    foreach ($session_info as $key => $value) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($session_check['valid']) {
        echo "<h2>👤 معلومات المستخدم الحالي</h2>";
        
        $current_user = $auth->getCurrentUser();
        
        if ($current_user) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px;'>المعلومة</th>";
            echo "<th style='padding: 10px;'>القيمة</th>";
            echo "</tr>";
            
            $user_info = [
                'المعرف' => $current_user['id'],
                'اسم المستخدم' => $current_user['username'],
                'الاسم الكامل' => $current_user['full_name'],
                'البريد الإلكتروني' => $current_user['email'],
                'معرف الدور' => $current_user['role_id'],
                'معرف الفرع' => $current_user['branch_id'] ?? 'غير محدد',
                'آخر تسجيل دخول' => $current_user['last_login'] ?? 'غير محدد'
            ];
            
            foreach ($user_info as $key => $value) {
                echo "<tr>";
                echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<h2>⏰ معلومات توقيت الجلسة</h2>";
        
        $current_time = time();
        $login_time = $_SESSION['login_time'] ?? $current_time;
        $last_activity = $_SESSION['last_activity'] ?? $login_time;
        
        $session_timeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600;
        $max_session_time = defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME : 86400;
        
        $time_since_login = $current_time - $login_time;
        $time_since_activity = $current_time - $last_activity;
        $time_until_timeout = $session_timeout - $time_since_activity;
        $time_until_max_expiry = $max_session_time - $time_since_login;
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>المعلومة</th>";
        echo "<th style='padding: 10px;'>القيمة</th>";
        echo "</tr>";
        
        $timing_info = [
            'وقت تسجيل الدخول' => date('Y-m-d H:i:s', $login_time),
            'آخر نشاط' => date('Y-m-d H:i:s', $last_activity),
            'الوقت الحالي' => date('Y-m-d H:i:s', $current_time),
            'مدة الجلسة' => gmdate('H:i:s', $time_since_login),
            'مدة عدم النشاط' => gmdate('H:i:s', $time_since_activity),
            'الوقت المتبقي للانتهاء' => $time_until_timeout > 0 ? gmdate('H:i:s', $time_until_timeout) : 'منتهية',
            'الوقت المتبقي للحد الأقصى' => $time_until_max_expiry > 0 ? gmdate('H:i:s', $time_until_max_expiry) : 'منتهية',
            'مهلة انتهاء الجلسة' => gmdate('H:i:s', $session_timeout),
            'الحد الأقصى للجلسة' => gmdate('H:i:s', $max_session_time),
            'عدد مرات التمديد' => $_SESSION['extension_count'] ?? 0
        ];
        
        foreach ($timing_info as $key => $value) {
            $color = '';
            if (strpos($key, 'المتبقي') !== false && $value === 'منتهية') {
                $color = 'color: red;';
            } elseif (strpos($key, 'المتبقي') !== false && strpos($value, ':') !== false) {
                $parts = explode(':', $value);
                $minutes = (int)$parts[1];
                if ($minutes < 5) {
                    $color = 'color: orange;';
                }
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
            echo "<td style='padding: 8px; $color'>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // تحذيرات
        if ($time_until_timeout <= 300 && $time_until_timeout > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
            echo "<h3>⚠️ تحذير</h3>";
            echo "<p>ستنتهي جلستك خلال أقل من 5 دقائق!</p>";
            echo "</div>";
        }
        
        if ($time_until_timeout <= 0) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
            echo "<h3>❌ انتهت الجلسة</h3>";
            echo "<p>انتهت صلاحية جلستك بسبب عدم النشاط</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
        echo "<h3>❌ لا توجد جلسة نشطة</h3>";
        echo "<p><strong>السبب:</strong> " . htmlspecialchars($session_check['message']) . "</p>";
        echo "</div>";
    }
    
    echo "<h2>🧪 اختبار وظائف الجلسة</h2>";
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔗 روابط الاختبار</h3>";
    
    if ($session_check['valid']) {
        echo "<p><a href='auth/logout.php' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🚪 تسجيل خروج عادي</a></p>";
        echo "<p><a href='auth/logout.php?reason=manual' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🚪 تسجيل خروج يدوي</a></p>";
        echo "<p><a href='auth/logout.php?reason=security' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔒 تسجيل خروج أمني</a></p>";
        echo "<p><a href='dashboard/api/check_session.php' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 فحص الجلسة (API)</a></p>";
        echo "<p><a href='dashboard/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 لوحة التحكم</a></p>";
    } else {
        echo "<p><a href='auth/login.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a></p>";
    }
    
    echo "<p><a href='test_session_management.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔄 تحديث الصفحة</a></p>";
    echo "</div>";
    
    echo "<h2>⚙️ إعدادات الجلسة</h2>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>الإعداد</th>";
    echo "<th style='padding: 10px;'>القيمة</th>";
    echo "</tr>";
    
    $session_settings = [
        'SESSION_TIMEOUT' => defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT . ' ثانية (' . gmdate('H:i:s', SESSION_TIMEOUT) . ')' : 'غير محدد',
        'MAX_SESSION_TIME' => defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME . ' ثانية (' . gmdate('H:i:s', MAX_SESSION_TIME) . ')' : 'غير محدد',
        'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime') . ' ثانية',
        'session.cookie_lifetime' => ini_get('session.cookie_lifetime') . ' ثانية',
        'session.use_cookies' => ini_get('session.use_cookies') ? 'مفعل' : 'معطل',
        'session.use_strict_mode' => ini_get('session.use_strict_mode') ? 'مفعل' : 'معطل',
        'session.cookie_secure' => ini_get('session.cookie_secure') ? 'مفعل' : 'معطل',
        'session.cookie_httponly' => ini_get('session.cookie_httponly') ? 'مفعل' : 'معطل'
    ];
    
    foreach ($session_settings as $key => $value) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في اختبار الجلسة</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    font-size: 0.9em;
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>

<script>
// تحديث الصفحة كل 30 ثانية لمراقبة تغييرات الجلسة
setTimeout(function() {
    window.location.reload();
}, 30000);

// عرض العد التنازلي
let countdown = 30;
const countdownElement = document.createElement('div');
countdownElement.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #007bff; color: white; padding: 10px; border-radius: 5px; z-index: 1000;';
countdownElement.innerHTML = 'تحديث تلقائي خلال: <span id="countdown">30</span> ثانية';
document.body.appendChild(countdownElement);

const interval = setInterval(function() {
    countdown--;
    document.getElementById('countdown').textContent = countdown;
    
    if (countdown <= 0) {
        clearInterval(interval);
    }
}, 1000);
</script>
