-- Trust Plus Financial Management System Database Schema - Part 2
-- الجداول المالية والمحاسبية

USE trust_plus;

-- جدول المعاملات المالية الرئيسية
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_date DATE NOT NULL,
    transaction_type ENUM('exchange', 'transfer', 'receipt', 'payment', 'journal') NOT NULL,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    total_amount_base_currency DECIMAL(15,2) NOT NULL,
    branch_id INT NOT NULL,
    user_id INT NOT NULL,
    customer_id INT NULL,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE transaction_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    account_id INT NOT NULL,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    currency_id INT NOT NULL,
    amount_foreign_currency DECIMAL(15,2),
    exchange_rate_used DECIMAL(15,6),
    narration TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- جدول عمليات صرف العملات
CREATE TABLE currency_exchange_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    customer_id INT NOT NULL,
    buy_currency_id INT NOT NULL,
    buy_amount DECIMAL(15,2) NOT NULL,
    sell_currency_id INT NOT NULL,
    sell_amount DECIMAL(15,2) NOT NULL,
    exchange_rate_used DECIMAL(15,6) NOT NULL,
    commission_amount DECIMAL(15,2) DEFAULT 0,
    commission_currency_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (buy_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (sell_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (commission_currency_id) REFERENCES currencies(id)
);

-- جدول التحويلات المالية
CREATE TABLE financial_transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    sender_customer_id INT NOT NULL,
    recipient_name VARCHAR(100) NOT NULL,
    recipient_account_details TEXT,
    recipient_bank VARCHAR(100),
    recipient_country VARCHAR(50),
    sending_currency_id INT NOT NULL,
    sending_amount DECIMAL(15,2) NOT NULL,
    receiving_currency_id INT NOT NULL,
    receiving_amount DECIMAL(15,2) NOT NULL,
    exchange_rate_used DECIMAL(15,6),
    transfer_fee_amount DECIMAL(15,2) DEFAULT 0,
    transfer_fee_currency_id INT NOT NULL,
    transfer_method ENUM('cash', 'bank_transfer', 'online') NOT NULL,
    status ENUM('pending', 'sent', 'received', 'paid', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_customer_id) REFERENCES customers(id),
    FOREIGN KEY (sending_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (receiving_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (transfer_fee_currency_id) REFERENCES currencies(id)
);

-- جدول الحسابات البنكية للشركة
CREATE TABLE bank_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_name VARCHAR(100) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    currency_id INT NOT NULL,
    initial_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    branch_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- جدول الصناديق النقدية
CREATE TABLE cash_boxes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    currency_id INT NOT NULL,
    initial_balance DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    branch_id INT NOT NULL,
    responsible_user_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (responsible_user_id) REFERENCES users(id)
);

-- جدول إعدادات النظام
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    updated_by_user_id INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by_user_id) REFERENCES users(id)
);

-- جدول سجلات التدقيق
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    old_value JSON,
    new_value JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول المستندات المرفقة
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    related_entity_type ENUM('customer', 'transaction', 'user') NOT NULL,
    related_entity_id INT NOT NULL,
    document_type ENUM('id_front', 'id_back', 'passport', 'utility_bill', 'bank_statement', 'other') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by_user_id INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by_user_id INT NULL,
    verified_date TIMESTAMP NULL,
    notes TEXT,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id),
    FOREIGN KEY (verified_by_user_id) REFERENCES users(id)
);

-- جدول فحوصات الامتثال
CREATE TABLE compliance_checks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    transaction_id INT NULL,
    check_type ENUM('kyc_verification', 'aml_screening', 'sanctions_check', 'pep_check', 'risk_assessment') NOT NULL,
    check_result ENUM('pass', 'fail', 'pending', 'requires_review') NOT NULL,
    check_details JSON,
    risk_score INT DEFAULT 0,
    performed_by_user_id INT NOT NULL,
    check_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date DATE NULL,
    notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(id),
    FOREIGN KEY (performed_by_user_id) REFERENCES users(id)
);

-- جدول تاريخ تحديثات العملاء
CREATE TABLE customer_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by_user_id INT NOT NULL,
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (changed_by_user_id) REFERENCES users(id)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'critical', 'success') DEFAULT 'info',
    related_entity_type VARCHAR(50),
    related_entity_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول أحداث النظام
CREATE TABLE system_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_type ENUM('customer_registered', 'kyc_approved', 'kyc_rejected', 'risk_level_changed', 'blacklist_added', 'blacklist_removed', 'large_transaction', 'suspicious_activity', 'exchange_transaction', 'rate_updated') NOT NULL,
    event_details JSON,
    customer_id INT NULL,
    transaction_id INT NULL,
    user_id INT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    source VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول أرباح الصرافة اليومية
CREATE TABLE daily_exchange_profits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    profit_date DATE NOT NULL,
    currency_pair VARCHAR(10) NOT NULL,
    from_currency_id INT NOT NULL,
    to_currency_id INT NOT NULL,
    total_transactions INT DEFAULT 0,
    total_volume_from DECIMAL(15,2) DEFAULT 0,
    total_volume_to DECIMAL(15,2) DEFAULT 0,
    total_commission DECIMAL(15,2) DEFAULT 0,
    total_spread_profit DECIMAL(15,2) DEFAULT 0,
    total_profit DECIMAL(15,2) DEFAULT 0,
    branch_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (to_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    UNIQUE KEY unique_daily_profit (profit_date, currency_pair, branch_id)
);

-- جدول إعدادات الصرافة
CREATE TABLE exchange_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    currency_pair VARCHAR(10) NOT NULL,
    from_currency_id INT NOT NULL,
    to_currency_id INT NOT NULL,
    default_spread_percentage DECIMAL(5,4) DEFAULT 0.0050,
    min_commission DECIMAL(15,2) DEFAULT 0,
    max_commission DECIMAL(15,2) DEFAULT 0,
    commission_percentage DECIMAL(5,4) DEFAULT 0.0025,
    daily_limit DECIMAL(15,2) DEFAULT 0,
    min_transaction_amount DECIMAL(15,2) DEFAULT 1,
    max_transaction_amount DECIMAL(15,2) DEFAULT 100000,
    is_active BOOLEAN DEFAULT TRUE,
    branch_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (to_currency_id) REFERENCES currencies(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    UNIQUE KEY unique_pair_branch (currency_pair, branch_id)
);
