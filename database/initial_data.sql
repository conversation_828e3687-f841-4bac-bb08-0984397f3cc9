-- Trust Plus Financial Management System - Initial Data
-- البيانات الأولية للنظام

USE trust_plus;

-- إدراج الأدوار الأساسية
INSERT INTO roles (role_name, description) VALUES
('admin', 'مدير النظام - صلاحيات كاملة'),
('manager', 'مدير الفرع - إدارة العمليات والموظفين'),
('accountant', 'محاسب - إدارة الحسابات والتقارير المالية'),
('cashier', 'أمين الصندوق - العمليات النقدية والصرافة'),
('operator', 'موظف تشغيلي - العمليات الأساسية');

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (permission_name, description, module) VALUES
-- صلاحيات إدارة المستخدمين
('users.view', 'عرض المستخدمين', 'users'),
('users.create', 'إضافة مستخدم جديد', 'users'),
('users.edit', 'تعديل بيانات المستخدمين', 'users'),
('users.delete', 'حذف المستخدمين', 'users'),
('users.manage_roles', 'إدارة أدوار المستخدمين', 'users'),

-- صلاحيات إدارة العملاء
('customers.view', 'عرض العملاء', 'customers'),
('customers.create', 'إضافة عميل جديد', 'customers'),
('customers.edit', 'تعديل بيانات العملاء', 'customers'),
('customers.delete', 'حذف العملاء', 'customers'),
('customers.kyc', 'إدارة عمليات التحقق من العملاء', 'customers'),

-- صلاحيات العمليات المالية
('transactions.view', 'عرض المعاملات المالية', 'transactions'),
('transactions.create', 'إنشاء معاملات مالية', 'transactions'),
('transactions.edit', 'تعديل المعاملات المالية', 'transactions'),
('transactions.delete', 'حذف المعاملات المالية', 'transactions'),
('transactions.approve', 'اعتماد المعاملات المالية', 'transactions'),

-- صلاحيات الصرافة
('exchange.view', 'عرض عمليات الصرافة', 'exchange'),
('exchange.create', 'إنشاء عمليات صرافة', 'exchange'),
('exchange.rates', 'إدارة أسعار الصرف', 'exchange'),

-- صلاحيات التحويلات
('transfers.view', 'عرض التحويلات المالية', 'transfers'),
('transfers.create', 'إنشاء تحويلات مالية', 'transfers'),
('transfers.edit', 'تعديل التحويلات المالية', 'transfers'),
('transfers.approve', 'اعتماد التحويلات المالية', 'transfers'),

-- صلاحيات التقارير
('reports.financial', 'التقارير المالية', 'reports'),
('reports.accounting', 'التقارير المحاسبية', 'reports'),
('reports.compliance', 'تقارير الامتثال', 'reports'),
('reports.audit', 'تقارير التدقيق', 'reports'),

-- صلاحيات الإعدادات
('settings.view', 'عرض إعدادات النظام', 'settings'),
('settings.edit', 'تعديل إعدادات النظام', 'settings'),
('settings.accounts', 'إدارة دليل الحسابات', 'settings'),
('settings.currencies', 'إدارة العملات', 'settings'),

-- صلاحيات إدارة الصناديق والبنوك
('cash.view', 'عرض الصناديق والبنوك', 'cash'),
('cash.manage', 'إدارة الصناديق والبنوك', 'cash'),
('cash.reconcile', 'تسوية الحسابات', 'cash');

-- ربط الأدوار بالصلاحيات
-- مدير النظام - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير الفرع - معظم الصلاحيات عدا إدارة النظام
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions 
WHERE permission_name NOT IN ('users.delete', 'settings.edit');

-- المحاسب - الصلاحيات المحاسبية والمالية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions 
WHERE module IN ('transactions', 'reports', 'cash', 'settings') 
   OR permission_name IN ('customers.view', 'exchange.view');

-- أمين الصندوق - العمليات النقدية والصرافة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions 
WHERE module IN ('exchange', 'cash') 
   OR permission_name IN ('customers.view', 'customers.create', 'transactions.view', 'transactions.create');

-- الموظف التشغيلي - العمليات الأساسية فقط
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions 
WHERE permission_name IN ('customers.view', 'transactions.view', 'exchange.view', 'transfers.view');

-- إدراج العملات الأساسية
INSERT INTO currencies (name, code, symbol, is_base_currency, decimal_places) VALUES
('الدولار الأمريكي', 'USD', '$', TRUE, 2),
('اليورو', 'EUR', '€', FALSE, 2),
('الجنيه الإسترليني', 'GBP', '£', FALSE, 2),
('الريال السعودي', 'SAR', 'ر.س', FALSE, 2),
('الدرهم الإماراتي', 'AED', 'د.إ', FALSE, 2),
('الدينار الكويتي', 'KWD', 'د.ك', FALSE, 3),
('الريال القطري', 'QAR', 'ر.ق', FALSE, 2),
('الدينار البحريني', 'BHD', 'د.ب', FALSE, 3);

-- إدراج الفرع الرئيسي
INSERT INTO branches (name, address, phone, email) VALUES
('الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+966123456789', '<EMAIL>');

-- إنشاء المستخدم الإداري الأول
-- كلمة المرور: admin123 (يجب تغييرها فور تسجيل الدخول الأول)
INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id) VALUES
('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'مدير النظام', 1, 1);

-- إعدادات النظام الأساسية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('company_name', 'Trust Plus', 'اسم الشركة'),
('company_address', 'العنوان الرئيسي للشركة', 'عنوان الشركة'),
('company_phone', '+966123456789', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
('base_currency', 'USD', 'العملة الأساسية للنظام'),
('financial_year_start', '01-01', 'بداية السنة المالية (شهر-يوم)'),
('max_daily_exchange_limit', '100000', 'الحد الأقصى للصرافة اليومية'),
('kyc_required_amount', '10000', 'المبلغ المطلوب للتحقق من الهوية'),
('session_timeout', '3600', 'مهلة انتهاء الجلسة بالثواني'),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي');
