<?php
/**
 * Trust Plus - Configuration File
 */

define("SYSTEM_NAME", "Trust Plus");
define("SYSTEM_VERSION", "2.0.0");
define("SYSTEM_LANGUAGE", "ar");
define("BASE_URL", "/");
define("DEFAULT_CURRENCY", "USD");
define("DATE_FORMAT", "Y-m-d");
define("TIME_FORMAT", "H:i:s");
define("TIMEZONE", "Asia/Riyadh");

// قاعدة البيانات
define("DB_HOST", "localhost");
define("DB_NAME", "trust_plus");
define("DB_USER", "root");
define("DB_PASS", "");

// الأمان
define("SESSION_TIMEOUT", 3600); // انتهاء الجلسة بعد ساعة من عدم النشاط
define("MAX_SESSION_TIME", 86400); // الحد الأقصى للجلسة 24 ساعة
define("SESSION_WARNING_TIME", 300); // تحذير قبل 5 دقائق من انتهاء الجلسة
define("MAX_LOGIN_ATTEMPTS", 5);
define("PRODUCTION", false);

if (!PRODUCTION) {
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
}

date_default_timezone_set(TIMEZONE);

// إعدادات الجلسة المتقدمة
if (!headers_sent()) {
    // تكوين إعدادات الجلسة
    ini_set('session.cookie_lifetime', 0); // انتهاء الكوكي عند إغلاق المتصفح
    ini_set('session.cookie_secure', 0); // تعيين إلى 1 في HTTPS
    ini_set('session.cookie_httponly', 1); // منع الوصول عبر JavaScript
    ini_set('session.use_strict_mode', 1); // وضع صارم للجلسة
    ini_set('session.cookie_samesite', 'Lax'); // حماية CSRF
    ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);

    // تعيين اسم الجلسة
    session_name('TRUST_PLUS_SESSION');
}
?>
