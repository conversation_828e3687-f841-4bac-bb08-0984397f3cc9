<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار JavaScript - Trust Plus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 1000px;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .btn-test {
            margin: 5px;
        }
    </style>
</head>
<body data-session-monitor="true" class="logged-in">
    <div class="container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-code text-primary me-2"></i>اختبار JavaScript</h1>
            <p class="text-muted">فحص حالة ملفات JavaScript ومراقب الجلسة</p>
        </div>
        
        <div id="jsStatus">
            <div class="status-item">
                <h4><i class="fas fa-spinner fa-spin me-2"></i>جاري فحص JavaScript...</h4>
            </div>
        </div>
        
        <div class="text-center mb-4">
            <button onclick="testBootstrap()" class="btn btn-primary btn-test">
                <i class="fab fa-bootstrap me-2"></i>اختبار Bootstrap
            </button>
            <button onclick="testSessionMonitor()" class="btn btn-success btn-test">
                <i class="fas fa-shield-alt me-2"></i>اختبار مراقب الجلسة
            </button>
            <button onclick="testFetch()" class="btn btn-info btn-test">
                <i class="fas fa-download me-2"></i>اختبار Fetch API
            </button>
            <button onclick="clearConsole()" class="btn btn-warning btn-test">
                <i class="fas fa-trash me-2"></i>مسح وحدة التحكم
            </button>
        </div>
        
        <div>
            <h4>وحدة تحكم JavaScript</h4>
            <div id="consoleOutput" class="console-output">
                <div>جاري تحميل وحدة التحكم...</div>
            </div>
        </div>
        
        <div class="mt-4">
            <h4>معلومات المتصفح</h4>
            <div id="browserInfo" class="status-item">
                جاري تحميل معلومات المتصفح...
            </div>
        </div>
    </div>
    
    <!-- تحميل Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل مراقب الجلسة -->
    <script src="assets/js/session-monitor-simple.js"></script>
    
    <script>
        // إعداد وحدة تحكم مخصصة
        const consoleOutput = document.getElementById('consoleOutput');
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                log: '#e2e8f0',
                error: '#fc8181',
                warn: '#f6e05e',
                info: '#63b3ed'
            };
            
            const div = document.createElement('div');
            div.style.color = colors[type] || colors.log;
            div.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // إعادة تعريف console
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        // دوال الاختبار
        function testBootstrap() {
            console.log('اختبار Bootstrap...');
            
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap متوفر');
                console.log('إصدار Bootstrap:', bootstrap.Tooltip.VERSION || 'غير محدد');
                
                // اختبار Modal
                try {
                    const testModal = document.createElement('div');
                    testModal.className = 'modal';
                    testModal.id = 'testModal';
                    document.body.appendChild(testModal);
                    
                    const modal = new bootstrap.Modal(testModal);
                    console.log('✅ Bootstrap Modal يعمل بشكل صحيح');
                    
                    document.body.removeChild(testModal);
                } catch (error) {
                    console.error('❌ خطأ في Bootstrap Modal:', error.message);
                }
            } else {
                console.error('❌ Bootstrap غير متوفر');
            }
        }
        
        function testSessionMonitor() {
            console.log('اختبار مراقب الجلسة...');
            
            if (typeof window.sessionMonitor !== 'undefined') {
                console.log('✅ مراقب الجلسة متوفر');
                console.log('حالة المراقب:', window.sessionMonitor.isActive ? 'نشط' : 'غير نشط');
                console.log('آخر نشاط:', new Date(window.sessionMonitor.lastActivity).toLocaleString());
                
                // اختبار الدوال
                if (typeof window.updateActivity === 'function') {
                    console.log('✅ دالة updateActivity متوفرة');
                    window.updateActivity();
                    console.log('تم تحديث النشاط');
                } else {
                    console.error('❌ دالة updateActivity غير متوفرة');
                }
                
                if (typeof window.extendSession === 'function') {
                    console.log('✅ دالة extendSession متوفرة');
                } else {
                    console.error('❌ دالة extendSession غير متوفرة');
                }
                
            } else {
                console.error('❌ مراقب الجلسة غير متوفر');
            }
        }
        
        async function testFetch() {
            console.log('اختبار Fetch API...');
            
            if (typeof fetch !== 'undefined') {
                console.log('✅ Fetch API متوفر');
                
                try {
                    console.log('اختبار الاتصال بـ API...');
                    const response = await fetch('dashboard/api/check_session.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'check_session'
                        })
                    });
                    
                    console.log('حالة الاستجابة:', response.status);
                    console.log('نوع المحتوى:', response.headers.get('content-type'));
                    
                    const result = await response.json();
                    console.log('نتيجة API:', result);
                    
                    if (result.success) {
                        console.log('✅ API يعمل بشكل صحيح');
                    } else {
                        console.warn('⚠️ API يعمل لكن هناك مشكلة:', result.message);
                    }
                    
                } catch (error) {
                    console.error('❌ خطأ في Fetch API:', error.message);
                }
            } else {
                console.error('❌ Fetch API غير متوفر');
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '<div>تم مسح وحدة التحكم</div>';
        }
        
        function updateStatus() {
            const statusDiv = document.getElementById('jsStatus');
            let html = '';
            
            // فحص JavaScript الأساسي
            html += '<div class="status-item status-success">';
            html += '<h5><i class="fas fa-check-circle text-success me-2"></i>JavaScript يعمل</h5>';
            html += '<p>JavaScript الأساسي يعمل بشكل صحيح</p>';
            html += '</div>';
            
            // فحص Bootstrap
            if (typeof bootstrap !== 'undefined') {
                html += '<div class="status-item status-success">';
                html += '<h5><i class="fas fa-check-circle text-success me-2"></i>Bootstrap متوفر</h5>';
                html += '<p>Bootstrap JS محمل ويعمل بشكل صحيح</p>';
                html += '</div>';
            } else {
                html += '<div class="status-item status-error">';
                html += '<h5><i class="fas fa-times-circle text-danger me-2"></i>Bootstrap غير متوفر</h5>';
                html += '<p>Bootstrap JS غير محمل أو لا يعمل</p>';
                html += '</div>';
            }
            
            // فحص مراقب الجلسة
            if (typeof window.sessionMonitor !== 'undefined') {
                html += '<div class="status-item status-success">';
                html += '<h5><i class="fas fa-check-circle text-success me-2"></i>مراقب الجلسة متوفر</h5>';
                html += '<p>مراقب الجلسة محمل ويعمل بشكل صحيح</p>';
                html += '</div>';
            } else {
                html += '<div class="status-item status-warning">';
                html += '<h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>مراقب الجلسة غير متوفر</h5>';
                html += '<p>مراقب الجلسة غير محمل أو لا يعمل</p>';
                html += '</div>';
            }
            
            // فحص Fetch API
            if (typeof fetch !== 'undefined') {
                html += '<div class="status-item status-success">';
                html += '<h5><i class="fas fa-check-circle text-success me-2"></i>Fetch API متوفر</h5>';
                html += '<p>Fetch API متوفر للاتصال بالخادم</p>';
                html += '</div>';
            } else {
                html += '<div class="status-item status-error">';
                html += '<h5><i class="fas fa-times-circle text-danger me-2"></i>Fetch API غير متوفر</h5>';
                html += '<p>Fetch API غير متوفر في هذا المتصفح</p>';
                html += '</div>';
            }
            
            statusDiv.innerHTML = html;
        }
        
        function updateBrowserInfo() {
            const browserDiv = document.getElementById('browserInfo');
            
            const info = {
                'اسم المتصفح': navigator.userAgent,
                'اللغة': navigator.language,
                'المنصة': navigator.platform,
                'Cookies مفعلة': navigator.cookieEnabled ? 'نعم' : 'لا',
                'JavaScript مفعل': 'نعم',
                'Local Storage': typeof localStorage !== 'undefined' ? 'متوفر' : 'غير متوفر',
                'Session Storage': typeof sessionStorage !== 'undefined' ? 'متوفر' : 'غير متوفر',
                'عرض الشاشة': screen.width + 'x' + screen.height,
                'عرض النافذة': window.innerWidth + 'x' + window.innerHeight
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<strong>${key}:</strong> ${value}<br>`;
            }
            
            browserDiv.innerHTML = html;
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');
            
            setTimeout(() => {
                updateStatus();
                updateBrowserInfo();
                console.log('تم تحديث معلومات الحالة');
            }, 1000);
            
            // اختبار تلقائي
            setTimeout(() => {
                console.log('بدء الاختبار التلقائي...');
                testBootstrap();
                testSessionMonitor();
            }, 2000);
        });
    </script>
</body>
</html>
