<?php
/**
 * Trust Plus - Main Index Page
 * الصفحة الرئيسية لنظام Trust Plus
 */

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من وجود الملفات الأساسية
$required_files = [
    'config.php',
    'includes/auth.php',
    'includes/database.php'
];

$missing_files = [];
foreach ($required_files as $file) {
    if (!file_exists($file)) {
        $missing_files[] = $file;
    }
}

// إذا كانت هناك ملفات مفقودة، عرض رسالة خطأ
if (!empty($missing_files)) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خطأ في النظام - Trust Plus</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: #f8f9fa;
                margin: 0;
                padding: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
            }
            .error-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                max-width: 600px;
                text-align: center;
            }
            .error-icon {
                font-size: 4rem;
                color: #dc3545;
                margin-bottom: 20px;
            }
            h1 {
                color: #dc3545;
                margin-bottom: 20px;
            }
            .missing-files {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                text-align: right;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px;
            }
            .btn:hover {
                background: #0056b3;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1>خطأ في تهيئة النظام</h1>
            <p>عذراً، هناك ملفات مفقودة مطلوبة لتشغيل النظام:</p>

            <div class="missing-files">
                <strong>الملفات المفقودة:</strong>
                <ul style="text-align: right; margin: 10px 0;">
                    <?php foreach ($missing_files as $file): ?>
                        <li><?php echo htmlspecialchars($file); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <p>يرجى التأكد من تحميل جميع ملفات النظام بشكل صحيح.</p>

            <a href="test_homepage.php" class="btn">🔍 اختبار النظام</a>
            <a href="setup/" class="btn">⚙️ إعداد النظام</a>
        </div>
    </body>
    </html>
    <?php
    exit();
}

try {
    // تحميل الملفات الأساسية
    require_once 'config.php';
    require_once 'includes/auth.php';

    $auth = new Auth();

    // التحقق من صحة الجلسة
    if (!$auth->checkSession()) {
        // إعادة توجيه إلى صفحة تسجيل الدخول
        header('Location: auth/login.php?message=' . urlencode('مرحباً بك في نظام Trust Plus'));
        exit();
    }

    // إعادة توجيه إلى لوحة التحكم
    header('Location: dashboard/index.php');
    exit();

} catch (Exception $e) {
    // في حالة حدوث خطأ، عرض صفحة خطأ مفصلة
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خطأ في النظام - Trust Plus</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: #f8f9fa;
                margin: 0;
                padding: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
            }
            .error-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                max-width: 800px;
                text-align: center;
            }
            .error-icon {
                font-size: 4rem;
                color: #dc3545;
                margin-bottom: 20px;
            }
            h1 {
                color: #dc3545;
                margin-bottom: 20px;
            }
            .error-details {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                text-align: right;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px;
            }
            .btn:hover {
                background: #0056b3;
            }
            .btn-success {
                background: #28a745;
            }
            .btn-success:hover {
                background: #1e7e34;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">💥</div>
            <h1>خطأ في تشغيل النظام</h1>
            <p>عذراً، حدث خطأ أثناء تحميل النظام:</p>

            <div class="error-details">
                <strong>تفاصيل الخطأ:</strong><br>
                <?php echo htmlspecialchars($e->getMessage()); ?><br><br>
                <strong>الملف:</strong> <?php echo htmlspecialchars($e->getFile()); ?><br>
                <strong>السطر:</strong> <?php echo $e->getLine(); ?>
            </div>

            <p>يرجى التحقق من إعدادات قاعدة البيانات والملفات المطلوبة.</p>

            <a href="test_homepage.php" class="btn">🔍 اختبار النظام</a>
            <a href="auth/login.php" class="btn btn-success">🔐 تسجيل الدخول</a>
        </div>
    </body>
    </html>
    <?php
    exit();
}
?>
