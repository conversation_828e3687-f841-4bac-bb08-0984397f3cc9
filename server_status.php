<?php
/**
 * Trust Plus - Server Status Check
 * فحص حالة الخادم
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🖥️ فحص حالة الخادم</h1>";
echo "<hr>";

// معلومات PHP
echo "<h2>📋 معلومات PHP</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>المعلومة</th>";
echo "<th style='padding: 10px;'>القيمة</th>";
echo "</tr>";

$php_info = [
    'إصدار PHP' => phpversion(),
    'نظام التشغيل' => php_uname(),
    'الخادم' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد',
    'المضيف' => $_SERVER['HTTP_HOST'] ?? 'غير محدد',
    'المسار' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
    'الذاكرة المحددة' => ini_get('memory_limit'),
    'الحد الأقصى لحجم الملف' => ini_get('upload_max_filesize'),
    'الحد الأقصى لوقت التنفيذ' => ini_get('max_execution_time') . ' ثانية',
    'المنطقة الزمنية' => date_default_timezone_get(),
    'التاريخ والوقت الحالي' => date('Y-m-d H:i:s')
];

foreach ($php_info as $key => $value) {
    echo "<tr>";
    echo "<td style='padding: 8px; font-weight: bold;'>$key</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
    echo "</tr>";
}
echo "</table>";

// فحص الإضافات المطلوبة
echo "<h2>🔌 الإضافات المطلوبة</h2>";

$required_extensions = [
    'pdo' => 'PDO Database',
    'pdo_mysql' => 'MySQL PDO Driver',
    'json' => 'JSON Support',
    'session' => 'Session Support',
    'mbstring' => 'Multibyte String',
    'openssl' => 'OpenSSL',
    'curl' => 'cURL',
    'gd' => 'GD Image Library'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>الإضافة</th>";
echo "<th style='padding: 10px;'>الوصف</th>";
echo "<th style='padding: 10px;'>الحالة</th>";
echo "</tr>";

foreach ($required_extensions as $ext => $desc) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅ مفعلة' : '❌ غير مفعلة';
    $color = $loaded ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td style='padding: 8px; font-weight: bold;'>$ext</td>";
    echo "<td style='padding: 8px;'>$desc</td>";
    echo "<td style='padding: 8px; color: $color;'>$status</td>";
    echo "</tr>";
}
echo "</table>";

// فحص قاعدة البيانات
echo "<h2>🗄️ فحص قاعدة البيانات</h2>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $host = 'localhost';
    $dbname = 'trust_plus';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>عدد الجداول:</strong> " . count($tables) . "</p>";
    
    if (count($tables) > 0) {
        echo "<details>";
        echo "<summary><strong>قائمة الجداول (انقر للعرض)</strong></summary>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        echo "</details>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// فحص الملفات والمجلدات
echo "<h2>📁 فحص الملفات والمجلدات</h2>";

$important_paths = [
    '.' => 'المجلد الرئيسي',
    'config.php' => 'ملف الإعدادات',
    'includes/' => 'مجلد الملفات المضمنة',
    'dashboard/' => 'مجلد لوحة التحكم',
    'auth/' => 'مجلد المصادقة',
    'assets/' => 'مجلد الأصول',
    'assets/css/' => 'ملفات CSS',
    'assets/js/' => 'ملفات JavaScript'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>المسار</th>";
echo "<th style='padding: 10px;'>الوصف</th>";
echo "<th style='padding: 10px;'>النوع</th>";
echo "<th style='padding: 10px;'>الحالة</th>";
echo "<th style='padding: 10px;'>الصلاحيات</th>";
echo "</tr>";

foreach ($important_paths as $path => $desc) {
    $exists = file_exists($path);
    $type = is_dir($path) ? 'مجلد' : (is_file($path) ? 'ملف' : 'غير محدد');
    $status = $exists ? '✅ موجود' : '❌ مفقود';
    $color = $exists ? 'green' : 'red';
    $permissions = $exists ? substr(sprintf('%o', fileperms($path)), -4) : '-';
    
    echo "<tr>";
    echo "<td style='padding: 8px; font-family: monospace;'>$path</td>";
    echo "<td style='padding: 8px;'>$desc</td>";
    echo "<td style='padding: 8px; text-align: center;'>$type</td>";
    echo "<td style='padding: 8px; color: $color;'>$status</td>";
    echo "<td style='padding: 8px; text-align: center; font-family: monospace;'>$permissions</td>";
    echo "</tr>";
}
echo "</table>";

// فحص متغيرات البيئة
echo "<h2>🌍 متغيرات البيئة المهمة</h2>";

$env_vars = [
    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'غير محدد',
    'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'غير محدد',
    'HTTP_USER_AGENT' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد', 0, 100) . '...',
    'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'غير محدد'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>المتغير</th>";
echo "<th style='padding: 10px;'>القيمة</th>";
echo "</tr>";

foreach ($env_vars as $var => $value) {
    echo "<tr>";
    echo "<td style='padding: 8px; font-weight: bold;'>$var</td>";
    echo "<td style='padding: 8px; font-family: monospace; word-break: break-all;'>" . htmlspecialchars($value) . "</td>";
    echo "</tr>";
}
echo "</table>";

// روابط سريعة للاختبار
echo "<h2>🔗 روابط سريعة للاختبار</h2>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<p><a href='index.php' target='_blank' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الصفحة الرئيسية</a></p>";
echo "<p><a href='test_homepage.php' target='_blank' style='background: #ff9800; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 اختبار شامل</a></p>";
echo "<p><a href='auth/login.php' target='_blank' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a></p>";
echo "<p><a href='dashboard/index.php' target='_blank' style='background: #9c27b0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📊 لوحة التحكم</a></p>";
echo "</div>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2 {
    color: #333;
}

table {
    font-size: 0.9em;
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

details {
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

summary {
    cursor: pointer;
    font-weight: bold;
    padding: 5px;
}

summary:hover {
    background: #e9ecef;
    border-radius: 3px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
