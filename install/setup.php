<?php
/**
 * ملف إعداد قاعدة البيانات - Trust Plus System
 * يجب تشغيل هذا الملف مرة واحدة فقط لإعداد النظام
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'trust_plus';

$setup_complete = false;
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // قراءة وتنفيذ ملف إنشاء قاعدة البيانات
        $schema_sql = file_get_contents('../database/trust_plus_schema.sql');
        $schema_part2_sql = file_get_contents('../database/trust_plus_schema_part2.sql');
        $initial_data_sql = file_get_contents('../database/initial_data.sql');
        
        // تنفيذ الاستعلامات
        $pdo->exec($schema_sql);
        $pdo->exec($schema_part2_sql);
        $pdo->exec($initial_data_sql);
        
        $success_message = 'تم إعداد قاعدة البيانات بنجاح!';
        $setup_complete = true;
        
    } catch (PDOException $e) {
        $error_message = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - Trust Plus</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-body {
            padding: 2rem;
        }
        
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h2><i class="fas fa-cogs me-2"></i>إعداد نظام Trust Plus</h2>
            <p>إعداد قاعدة البيانات والبيانات الأولية</p>
        </div>
        
        <div class="setup-body">
            <?php if ($error_message): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$setup_complete): ?>
                <div class="mb-4">
                    <h5>متطلبات النظام:</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            PHP 7.4+
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            MySQL 5.7+
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            PDO Extension
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                        </li>
                    </ul>
                </div>
                
                <div class="mb-4">
                    <h5>إعدادات قاعدة البيانات:</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الخادم:</strong></td>
                                <td><?php echo $host; ?></td>
                            </tr>
                            <tr>
                                <td><strong>اسم المستخدم:</strong></td>
                                <td><?php echo $username; ?></td>
                            </tr>
                            <tr>
                                <td><strong>اسم قاعدة البيانات:</strong></td>
                                <td><?php echo $database; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> تأكد من أن خادم MySQL يعمل وأن إعدادات الاتصال صحيحة قبل المتابعة.
                </div>
                
                <form method="POST">
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-setup">
                            <i class="fas fa-play me-2"></i>
                            بدء إعداد قاعدة البيانات
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="alert alert-info" role="alert">
                    <h5><i class="fas fa-info-circle me-2"></i>تم إعداد النظام بنجاح!</h5>
                    <p class="mb-3">يمكنك الآن تسجيل الدخول باستخدام البيانات التالية:</p>
                    <ul class="mb-3">
                        <li><strong>اسم المستخدم:</strong> admin</li>
                        <li><strong>كلمة المرور:</strong> admin123</li>
                    </ul>
                    <p class="mb-0"><strong>مهم:</strong> يرجى تغيير كلمة المرور فور تسجيل الدخول الأول.</p>
                </div>
                
                <div class="d-grid">
                    <a href="../auth/login.php" class="btn btn-success">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        الانتقال إلى صفحة تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
