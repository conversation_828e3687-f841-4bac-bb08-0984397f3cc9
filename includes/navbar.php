<?php
/**
 * Trust Plus - Top Navigation Bar
 * شريط التنقل العلوي
 */

// التأكد من وجود متغير المصادقة
if (!isset($auth)) {
    require_once 'auth.php';
    $auth = new Auth();
}

if (!isset($current_user)) {
    $current_user = $auth->getCurrentUser();
}
?>

<!-- شريط التنقل العلوي -->
<nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container-fluid">
        <!-- زر تبديل الشريط الجانبي -->
        <button class="btn btn-outline-secondary me-3" data-sidebar-toggle>
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- عنوان الصفحة -->
        <div class="navbar-brand mb-0">
            <?php if (isset($page_header)): ?>
                <?php if (isset($page_icon)): ?>
                    <i class="<?php echo htmlspecialchars($page_icon); ?> me-2"></i>
                <?php endif; ?>
                <span class="fw-bold"><?php echo htmlspecialchars($page_header); ?></span>
            <?php else: ?>
                <span class="fw-bold">Trust Plus</span>
            <?php endif; ?>
        </div>
        
        <!-- مساحة فارغة للتوسيط -->
        <div class="flex-grow-1"></div>
        
        <!-- أدوات الشريط العلوي -->
        <div class="navbar-nav">
            <!-- البحث السريع -->
            <div class="nav-item dropdown me-3">
                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-search"></i>
                    <span class="d-none d-md-inline ms-2">البحث السريع</span>
                </button>
                <div class="dropdown-menu dropdown-menu-end p-3" style="min-width: 300px;">
                    <form class="d-flex">
                        <input class="form-control me-2" type="search" placeholder="البحث في النظام..." aria-label="Search">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                    <hr>
                    <h6 class="dropdown-header">البحث السريع في:</h6>
                    <a class="dropdown-item" href="customers.php">
                        <i class="fas fa-users me-2"></i>العملاء
                    </a>
                    <a class="dropdown-item" href="transactions.php">
                        <i class="fas fa-receipt me-2"></i>المعاملات
                    </a>
                    <a class="dropdown-item" href="transfers.php">
                        <i class="fas fa-paper-plane me-2"></i>التحويلات
                    </a>
                </div>
            </div>
            
            <!-- الإشعارات -->
            <div class="nav-item dropdown me-3">
                <button class="btn btn-outline-secondary position-relative" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-bell"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                        <span class="visually-hidden">إشعارات غير مقروءة</span>
                    </span>
                </button>
                <div class="dropdown-menu dropdown-menu-end" style="min-width: 350px;">
                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                        <span>الإشعارات</span>
                        <small><a href="#" class="text-decoration-none">تحديد الكل كمقروء</a></small>
                    </div>
                    <div class="dropdown-divider"></div>
                    
                    <!-- إشعار 1 -->
                    <a class="dropdown-item py-3 border-bottom" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-exchange-alt text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">عملية صرافة جديدة</h6>
                                <p class="mb-1 text-muted small">تم إنجاز عملية صرافة بقيمة $1,500</p>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                        </div>
                    </a>
                    
                    <!-- إشعار 2 -->
                    <a class="dropdown-item py-3 border-bottom" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">تحديث أسعار الصرف</h6>
                                <p class="mb-1 text-muted small">يجب تحديث أسعار الصرف اليومية</p>
                                <small class="text-muted">منذ ساعة</small>
                            </div>
                        </div>
                    </a>
                    
                    <!-- إشعار 3 -->
                    <a class="dropdown-item py-3" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">تحويل مكتمل</h6>
                                <p class="mb-1 text-muted small">تم إكمال التحويل رقم #TF-2024-001</p>
                                <small class="text-muted">منذ ساعتين</small>
                            </div>
                        </div>
                    </a>
                    
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item text-center" href="notifications.php">
                        عرض جميع الإشعارات
                    </a>
                </div>
            </div>
            
            <!-- الإعدادات السريعة -->
            <div class="nav-item dropdown me-3">
                <button class="btn btn-outline-secondary" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                    <h6 class="dropdown-header">الإعدادات السريعة</h6>
                    <a class="dropdown-item" href="settings.php">
                        <i class="fas fa-cog me-2"></i>إعدادات النظام
                    </a>
                    <a class="dropdown-item" href="exchange_rates.php">
                        <i class="fas fa-chart-line me-2"></i>أسعار الصرف
                    </a>
                    <a class="dropdown-item" href="currencies.php">
                        <i class="fas fa-coins me-2"></i>إدارة العملات
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="backup.php">
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    </a>
                </div>
            </div>
            
            <!-- ملف المستخدم -->
            <div class="nav-item dropdown">
                <button class="btn btn-outline-primary dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="user-avatar me-2">
                        <?php if (!empty($current_user['avatar'])): ?>
                            <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>" alt="Avatar" class="rounded-circle" width="32" height="32">
                        <?php else: ?>
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 32px; height: 32px;">
                                <?php echo strtoupper(substr($current_user['full_name'], 0, 1)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="user-info text-start d-none d-md-block">
                        <div class="fw-bold"><?php echo htmlspecialchars($current_user['full_name']); ?></div>
                        <small class="text-muted"><?php echo htmlspecialchars($current_user['role_name'] ?? $current_user['role']); ?></small>
                    </div>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                    <!-- معلومات المستخدم -->
                    <div class="dropdown-header">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                <?php if (!empty($current_user['avatar'])): ?>
                                    <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>" alt="Avatar" class="rounded-circle" width="48" height="48">
                                <?php else: ?>
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 48px; height: 48px;">
                                        <?php echo strtoupper(substr($current_user['full_name'], 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars($current_user['full_name']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($current_user['username']); ?></small>
                                <br>
                                <small class="text-muted"><?php echo htmlspecialchars($current_user['role_name'] ?? $current_user['role']); ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    
                    <!-- روابط الملف الشخصي -->
                    <a class="dropdown-item" href="profile.php">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a>
                    <a class="dropdown-item" href="change_password.php">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a>
                    <a class="dropdown-item" href="preferences.php">
                        <i class="fas fa-sliders-h me-2"></i>التفضيلات
                    </a>
                    
                    <div class="dropdown-divider"></div>
                    
                    <!-- معلومات الجلسة -->
                    <div class="dropdown-item-text">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            آخر دخول: <?php echo date('Y-m-d H:i', strtotime($current_user['last_login'] ?? 'now')); ?>
                        </small>
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            IP: <?php echo $_SERVER['REMOTE_ADDR']; ?>
                        </small>
                    </div>
                    
                    <div class="dropdown-divider"></div>
                    
                    <!-- تسجيل الخروج -->
                    <a class="dropdown-item text-danger" href="../auth/logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- شريط المعلومات السريعة (اختياري) -->
<?php if (isset($show_info_bar) && $show_info_bar): ?>
<div class="info-bar bg-light border-bottom">
    <div class="container-fluid">
        <div class="row align-items-center py-2">
            <div class="col-md-6">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    اليوم: <?php echo date('l, F j, Y'); ?>
                </small>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"><?php echo date('H:i:s'); ?></span>
                </small>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الوقت كل ثانية
setInterval(function() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}, 1000);
</script>
<?php endif; ?>
