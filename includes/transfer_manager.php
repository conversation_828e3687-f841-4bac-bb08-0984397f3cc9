<?php
/**
 * فئة إدارة التحويلات المالية مع تتبع الحالات - Trust Plus System
 */

require_once 'auth.php';

class TransferManager {
    private $db;
    private $auth;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->auth = new Auth();
    }
    
    /**
     * إنشاء تحويل مالي جديد
     */
    public function createTransfer($data, $user_id) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من صحة البيانات
            $validation = $this->validateTransferData($data);
            if (!$validation['valid']) {
                throw new Exception($validation['message']);
            }
            
            // التحقق من حالة العميل المرسل
            $sender_check = $this->validateSender($data['sender_customer_id'], $data['sending_amount']);
            if (!$sender_check['valid']) {
                throw new Exception($sender_check['message']);
            }
            
            // الحصول على إعدادات التحويل
            $settings = $this->getTransferSettings($data['sending_currency_id'], $data['receiving_currency_id'], $data['recipient_country']);
            if (!$settings) {
                throw new Exception('إعدادات التحويل غير متوفرة لهذا المسار');
            }
            
            // حساب المبالغ والرسوم
            $calculations = $this->calculateTransferAmounts($data, $settings);
            
            // التحقق من الحدود
            $limit_check = $this->checkTransferLimits($data['sending_currency_id'], $calculations['sending_amount'], $user_id);
            if (!$limit_check['valid']) {
                throw new Exception($limit_check['message']);
            }
            
            // إنشاء رقم مرجعي فريد
            $reference_number = $this->generateReferenceNumber('TR');
            
            // إنشاء المعاملة الرئيسية
            $stmt = $this->db->prepare("
                INSERT INTO transactions (
                    transaction_date, transaction_type, reference_number, description,
                    total_amount_base_currency, branch_id, user_id, customer_id, status
                ) VALUES (
                    CURDATE(), 'transfer', :reference_number, :description,
                    :total_amount, :branch_id, :user_id, :customer_id, 'pending'
                )
            ");
            
            $description = sprintf(
                'تحويل مالي: %s %s إلى %s - المستفيد: %s',
                number_format($calculations['sending_amount'], 2),
                $calculations['sending_currency_code'],
                $data['recipient_country'],
                $data['recipient_name']
            );
            
            $stmt->bindParam(':reference_number', $reference_number);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':total_amount', $calculations['total_amount_base']);
            $stmt->bindParam(':branch_id', $data['branch_id']);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':customer_id', $data['sender_customer_id']);
            $stmt->execute();
            
            $transaction_id = $this->db->lastInsertId();
            
            // إنشاء تفاصيل التحويل
            $stmt = $this->db->prepare("
                INSERT INTO financial_transfers (
                    transaction_id, sender_customer_id, recipient_name, recipient_account_details,
                    recipient_bank, recipient_country, sending_currency_id, sending_amount,
                    receiving_currency_id, receiving_amount, exchange_rate_used,
                    transfer_fee_amount, transfer_fee_currency_id, transfer_method, status
                ) VALUES (
                    :transaction_id, :sender_customer_id, :recipient_name, :recipient_account_details,
                    :recipient_bank, :recipient_country, :sending_currency_id, :sending_amount,
                    :receiving_currency_id, :receiving_amount, :exchange_rate_used,
                    :transfer_fee_amount, :transfer_fee_currency_id, :transfer_method, 'pending'
                )
            ");
            
            $stmt->bindParam(':transaction_id', $transaction_id);
            $stmt->bindParam(':sender_customer_id', $data['sender_customer_id']);
            $stmt->bindParam(':recipient_name', $data['recipient_name']);
            $stmt->bindParam(':recipient_account_details', $data['recipient_account_details']);
            $stmt->bindParam(':recipient_bank', $data['recipient_bank']);
            $stmt->bindParam(':recipient_country', $data['recipient_country']);
            $stmt->bindParam(':sending_currency_id', $data['sending_currency_id']);
            $stmt->bindParam(':sending_amount', $calculations['sending_amount']);
            $stmt->bindParam(':receiving_currency_id', $data['receiving_currency_id']);
            $stmt->bindParam(':receiving_amount', $calculations['receiving_amount']);
            $stmt->bindParam(':exchange_rate_used', $calculations['exchange_rate_used']);
            $stmt->bindParam(':transfer_fee_amount', $calculations['transfer_fee']);
            $stmt->bindParam(':transfer_fee_currency_id', $data['sending_currency_id']);
            $stmt->bindParam(':transfer_method', $data['transfer_method']);
            $stmt->execute();
            
            $transfer_id = $this->db->lastInsertId();
            
            // إنشاء أول حالة في تتبع الحالات
            $this->addStatusHistory($transfer_id, null, 'pending', $user_id, 'تم إنشاء التحويل بنجاح');
            
            // إنشاء القيود المحاسبية
            $this->createAccountingEntries($transaction_id, $calculations);
            
            // تحديث أرباح التحويلات اليومية
            $this->updateDailyTransferProfits($calculations, $data['branch_id']);
            
            // إرسال إشعار للعميل
            $this->sendTransferNotification($transfer_id, 'created');
            
            // تسجيل الحدث
            $this->logSystemEvent('transfer_created', [
                'transaction_id' => $transaction_id,
                'transfer_id' => $transfer_id,
                'corridor' => $calculations['corridor'],
                'amount' => $calculations['sending_amount'],
                'recipient' => $data['recipient_name']
            ], $data['sender_customer_id'], $transaction_id, $user_id);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم إنشاء التحويل بنجاح',
                'transaction_id' => $transaction_id,
                'transfer_id' => $transfer_id,
                'reference_number' => $reference_number,
                'calculations' => $calculations
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * تحديث حالة التحويل
     */
    public function updateTransferStatus($transfer_id, $new_status, $user_id, $notes = '', $location = '') {
        try {
            $this->db->beginTransaction();
            
            // الحصول على الحالة الحالية
            $stmt = $this->db->prepare("SELECT status FROM financial_transfers WHERE id = :transfer_id");
            $stmt->bindParam(':transfer_id', $transfer_id);
            $stmt->execute();
            $current_transfer = $stmt->fetch();
            
            if (!$current_transfer) {
                throw new Exception('التحويل غير موجود');
            }
            
            $old_status = $current_transfer['status'];
            
            // التحقق من صحة التحديث
            if (!$this->isValidStatusTransition($old_status, $new_status)) {
                throw new Exception('لا يمكن تحديث الحالة من ' . $old_status . ' إلى ' . $new_status);
            }
            
            // تحديث حالة التحويل
            $stmt = $this->db->prepare("
                UPDATE financial_transfers 
                SET status = :new_status, updated_at = CURRENT_TIMESTAMP 
                WHERE id = :transfer_id
            ");
            $stmt->bindParam(':new_status', $new_status);
            $stmt->bindParam(':transfer_id', $transfer_id);
            $stmt->execute();
            
            // تحديث حالة المعاملة الرئيسية إذا لزم الأمر
            if ($new_status == 'paid') {
                $stmt = $this->db->prepare("
                    UPDATE transactions t
                    JOIN financial_transfers ft ON t.id = ft.transaction_id
                    SET t.status = 'completed'
                    WHERE ft.id = :transfer_id
                ");
                $stmt->bindParam(':transfer_id', $transfer_id);
                $stmt->execute();
            }
            
            // إضافة سجل في تتبع الحالات
            $estimated_completion = $this->calculateEstimatedCompletion($new_status);
            $this->addStatusHistory($transfer_id, $old_status, $new_status, $user_id, $notes, $location, $estimated_completion);
            
            // إرسال إشعار
            $this->sendTransferNotification($transfer_id, 'status_updated');
            
            // تسجيل الحدث
            $this->logSystemEvent('transfer_status_updated', [
                'transfer_id' => $transfer_id,
                'old_status' => $old_status,
                'new_status' => $new_status,
                'notes' => $notes
            ], null, null, $user_id);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم تحديث حالة التحويل بنجاح',
                'old_status' => $old_status,
                'new_status' => $new_status
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * حساب مبالغ التحويل والرسوم
     */
    public function calculateTransferAmounts($data, $settings) {
        // الحصول على سعر الصرف
        $exchange_rate = $this->getExchangeRate($data['sending_currency_id'], $data['receiving_currency_id']);
        if (!$exchange_rate) {
            throw new Exception('سعر الصرف غير متوفر');
        }
        
        // تطبيق هامش الربح على سعر الصرف
        $rate_with_margin = $exchange_rate * (1 - $settings['exchange_margin']);
        
        // حساب المبلغ المستلم
        $receiving_amount = $data['sending_amount'] * $rate_with_margin;
        
        // حساب رسوم التحويل
        $percentage_fee = $data['sending_amount'] * $settings['percentage_fee'];
        $transfer_fee = max($percentage_fee, $settings['base_fee']);
        
        // حساب الأرباح
        $exchange_profit = $data['sending_amount'] * $exchange_rate * $settings['exchange_margin'];
        $fee_profit = $transfer_fee - ($settings['partner_commission'] ?? 0);
        $total_profit = $exchange_profit + $fee_profit;
        
        // تحويل إلى العملة الأساسية للمحاسبة
        $total_amount_base = $this->convertToBaseCurrency($data['sending_amount'], $data['sending_currency_id']);
        
        return [
            'sending_amount' => round($data['sending_amount'], 2),
            'receiving_amount' => round($receiving_amount, 2),
            'exchange_rate_used' => $rate_with_margin,
            'transfer_fee' => round($transfer_fee, 2),
            'exchange_profit' => round($exchange_profit, 2),
            'fee_profit' => round($fee_profit, 2),
            'total_profit' => round($total_profit, 2),
            'total_amount_base' => round($total_amount_base, 2),
            'corridor' => $this->getCurrencyCode($data['sending_currency_id']) . '_' . $this->getCurrencyCode($data['receiving_currency_id']),
            'sending_currency_code' => $this->getCurrencyCode($data['sending_currency_id']),
            'receiving_currency_code' => $this->getCurrencyCode($data['receiving_currency_id'])
        ];
    }
    
    /**
     * الحصول على تفاصيل التحويل مع تاريخ الحالات
     */
    public function getTransferDetails($transfer_id) {
        try {
            // الحصول على تفاصيل التحويل الأساسية
            $stmt = $this->db->prepare("
                SELECT ft.*, t.reference_number, t.transaction_date, t.created_at,
                       c.full_name as sender_name, c.phone as sender_phone, c.email as sender_email,
                       sc.name as sending_currency_name, sc.code as sending_currency_code, sc.symbol as sending_currency_symbol,
                       rc.name as receiving_currency_name, rc.code as receiving_currency_code, rc.symbol as receiving_currency_symbol,
                       u.full_name as created_by_name, b.name as branch_name
                FROM financial_transfers ft
                JOIN transactions t ON ft.transaction_id = t.id
                JOIN customers c ON ft.sender_customer_id = c.id
                JOIN currencies sc ON ft.sending_currency_id = sc.id
                JOIN currencies rc ON ft.receiving_currency_id = rc.id
                JOIN users u ON t.user_id = u.id
                JOIN branches b ON t.branch_id = b.id
                WHERE ft.id = :transfer_id
            ");
            $stmt->bindParam(':transfer_id', $transfer_id);
            $stmt->execute();
            
            $transfer = $stmt->fetch();
            if (!$transfer) {
                return null;
            }
            
            // الحصول على تاريخ الحالات
            $stmt = $this->db->prepare("
                SELECT tsh.*, u.full_name as changed_by_name
                FROM transfer_status_history tsh
                JOIN users u ON tsh.changed_by_user_id = u.id
                WHERE tsh.transfer_id = :transfer_id
                ORDER BY tsh.status_date ASC
            ");
            $stmt->bindParam(':transfer_id', $transfer_id);
            $stmt->execute();
            
            $transfer['status_history'] = $stmt->fetchAll();
            
            return $transfer;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * البحث عن التحويلات مع فلاتر متقدمة
     */
    public function searchTransfers($filters = [], $limit = 50, $offset = 0) {
        try {
            $where_conditions = ['1=1'];
            $params = [];
            
            if (!empty($filters['reference_number'])) {
                $where_conditions[] = 't.reference_number LIKE :reference_number';
                $params[':reference_number'] = '%' . $filters['reference_number'] . '%';
            }
            
            if (!empty($filters['sender_name'])) {
                $where_conditions[] = 'c.full_name LIKE :sender_name';
                $params[':sender_name'] = '%' . $filters['sender_name'] . '%';
            }
            
            if (!empty($filters['recipient_name'])) {
                $where_conditions[] = 'ft.recipient_name LIKE :recipient_name';
                $params[':recipient_name'] = '%' . $filters['recipient_name'] . '%';
            }
            
            if (!empty($filters['status'])) {
                $where_conditions[] = 'ft.status = :status';
                $params[':status'] = $filters['status'];
            }
            
            if (!empty($filters['date_from'])) {
                $where_conditions[] = 't.transaction_date >= :date_from';
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $where_conditions[] = 't.transaction_date <= :date_to';
                $params[':date_to'] = $filters['date_to'];
            }
            
            if (!empty($filters['branch_id'])) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $filters['branch_id'];
            }
            
            if (!empty($filters['corridor'])) {
                $corridor_parts = explode('_', $filters['corridor']);
                if (count($corridor_parts) == 2) {
                    $where_conditions[] = 'sc.code = :from_currency AND rc.code = :to_currency';
                    $params[':from_currency'] = $corridor_parts[0];
                    $params[':to_currency'] = $corridor_parts[1];
                }
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $stmt = $this->db->prepare("
                SELECT ft.*, t.reference_number, t.transaction_date, t.created_at,
                       c.full_name as sender_name,
                       sc.code as sending_currency_code, sc.symbol as sending_currency_symbol,
                       rc.code as receiving_currency_code, rc.symbol as receiving_currency_symbol,
                       b.name as branch_name
                FROM financial_transfers ft
                JOIN transactions t ON ft.transaction_id = t.id
                JOIN customers c ON ft.sender_customer_id = c.id
                JOIN currencies sc ON ft.sending_currency_id = sc.id
                JOIN currencies rc ON ft.receiving_currency_id = rc.id
                JOIN branches b ON t.branch_id = b.id
                $where_clause
                ORDER BY t.created_at DESC
                LIMIT :limit OFFSET :offset
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function validateTransferData($data) {
        $required_fields = ['sender_customer_id', 'recipient_name', 'recipient_country', 
                           'sending_currency_id', 'receiving_currency_id', 'sending_amount', 'transfer_method'];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => "الحقل {$field} مطلوب"];
            }
        }
        
        if ($data['sending_amount'] <= 0) {
            return ['valid' => false, 'message' => 'مبلغ الإرسال يجب أن يكون أكبر من صفر'];
        }
        
        if ($data['sending_currency_id'] == $data['receiving_currency_id']) {
            return ['valid' => false, 'message' => 'عملة الإرسال والاستلام يجب أن تكونا مختلفتين'];
        }
        
        return ['valid' => true, 'message' => 'البيانات صحيحة'];
    }
    
    private function validateSender($customer_id, $amount) {
        try {
            $stmt = $this->db->prepare("
                SELECT kyc_status, blacklisted, risk_level
                FROM customers
                WHERE id = :customer_id
            ");
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            $customer = $stmt->fetch();
            
            if (!$customer) {
                return ['valid' => false, 'message' => 'العميل غير موجود'];
            }
            
            if ($customer['blacklisted']) {
                return ['valid' => false, 'message' => 'العميل في القائمة السوداء'];
            }
            
            if ($customer['kyc_status'] !== 'approved' && $amount > 10000) {
                return ['valid' => false, 'message' => 'يتطلب اعتماد KYC للتحويلات الكبيرة'];
            }
            
            return ['valid' => true, 'message' => 'العميل مؤهل للتحويل'];
            
        } catch (Exception $e) {
            return ['valid' => false, 'message' => 'خطأ في التحقق من العميل'];
        }
    }
    
    private function addStatusHistory($transfer_id, $old_status, $new_status, $user_id, $notes = '', $location = '', $estimated_completion = null) {
        $stmt = $this->db->prepare("
            INSERT INTO transfer_status_history (
                transfer_id, old_status, new_status, changed_by_user_id, 
                notes, location, estimated_completion_date
            ) VALUES (
                :transfer_id, :old_status, :new_status, :user_id, 
                :notes, :location, :estimated_completion
            )
        ");
        
        $stmt->bindParam(':transfer_id', $transfer_id);
        $stmt->bindParam(':old_status', $old_status);
        $stmt->bindParam(':new_status', $new_status);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':location', $location);
        $stmt->bindParam(':estimated_completion', $estimated_completion);
        $stmt->execute();
    }
    
    private function generateReferenceNumber($prefix) {
        return $prefix . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    private function getCurrencyCode($currency_id) {
        $stmt = $this->db->prepare("SELECT code FROM currencies WHERE id = :id");
        $stmt->bindParam(':id', $currency_id);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result ? $result['code'] : '';
    }
    
    private function convertToBaseCurrency($amount, $currency_id) {
        if ($currency_id == BASE_CURRENCY_ID) {
            return $amount;
        }
        
        $rate = $this->getExchangeRate($currency_id, BASE_CURRENCY_ID);
        return $rate ? $amount * $rate : $amount;
    }
    
    private function getExchangeRate($from_currency_id, $to_currency_id) {
        $stmt = $this->db->prepare("
            SELECT sell_rate FROM exchange_rates
            WHERE from_currency_id = :from_currency_id 
              AND to_currency_id = :to_currency_id
              AND is_active = 1
              AND effective_date <= CURDATE()
            ORDER BY effective_date DESC
            LIMIT 1
        ");
        
        $stmt->bindParam(':from_currency_id', $from_currency_id);
        $stmt->bindParam(':to_currency_id', $to_currency_id);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result ? $result['sell_rate'] : null;
    }
    
    private function getTransferSettings($from_currency_id, $to_currency_id, $to_country) {
        $corridor = $this->getCurrencyCode($from_currency_id) . '_' . $this->getCurrencyCode($to_currency_id);
        
        $stmt = $this->db->prepare("
            SELECT * FROM transfer_settings
            WHERE corridor = :corridor AND to_country = :to_country AND is_active = 1
        ");
        $stmt->bindParam(':corridor', $corridor);
        $stmt->bindParam(':to_country', $to_country);
        $stmt->execute();
        
        $settings = $stmt->fetch();
        
        // إعدادات افتراضية إذا لم توجد
        if (!$settings) {
            return [
                'base_fee' => 5.00,
                'percentage_fee' => 0.0050,
                'exchange_margin' => 0.0025,
                'processing_time_hours' => 24,
                'partner_commission' => 0
            ];
        }
        
        return $settings;
    }
    
    private function isValidStatusTransition($old_status, $new_status) {
        $valid_transitions = [
            'pending' => ['sent', 'cancelled'],
            'sent' => ['received', 'cancelled'],
            'received' => ['paid', 'cancelled'],
            'paid' => [],
            'cancelled' => []
        ];
        
        return in_array($new_status, $valid_transitions[$old_status] ?? []);
    }
    
    private function calculateEstimatedCompletion($status) {
        $hours_map = [
            'pending' => 2,
            'sent' => 24,
            'received' => 48,
            'paid' => 0
        ];
        
        $hours = $hours_map[$status] ?? 24;
        return $hours > 0 ? date('Y-m-d H:i:s', strtotime("+{$hours} hours")) : null;
    }
    
    private function checkTransferLimits($currency_id, $amount, $user_id) {
        // التحقق من الحدود اليومية
        // يمكن تطوير هذه الدالة حسب المتطلبات
        return ['valid' => true, 'message' => 'ضمن الحدود المسموحة'];
    }
    
    private function createAccountingEntries($transaction_id, $calculations) {
        // إنشاء القيود المحاسبية
        // يمكن تطوير هذه الدالة لاحقاً مع نظام المحاسبة
    }
    
    private function updateDailyTransferProfits($calculations, $branch_id) {
        $stmt = $this->db->prepare("
            INSERT INTO daily_transfer_profits (
                profit_date, corridor, from_currency_id, to_currency_id,
                total_transfers, total_volume_sent, total_volume_received,
                total_fees, total_exchange_profit, total_profit, branch_id
            ) VALUES (
                CURDATE(), :corridor, :from_currency_id, :to_currency_id,
                1, :volume_sent, :volume_received, :fees, :exchange_profit, :total_profit, :branch_id
            ) ON DUPLICATE KEY UPDATE
                total_transfers = total_transfers + 1,
                total_volume_sent = total_volume_sent + :volume_sent,
                total_volume_received = total_volume_received + :volume_received,
                total_fees = total_fees + :fees,
                total_exchange_profit = total_exchange_profit + :exchange_profit,
                total_profit = total_profit + :total_profit
        ");
        
        $stmt->bindParam(':corridor', $calculations['corridor']);
        $stmt->bindParam(':from_currency_id', $calculations['sending_currency_code']);
        $stmt->bindParam(':to_currency_id', $calculations['receiving_currency_code']);
        $stmt->bindParam(':volume_sent', $calculations['sending_amount']);
        $stmt->bindParam(':volume_received', $calculations['receiving_amount']);
        $stmt->bindParam(':fees', $calculations['transfer_fee']);
        $stmt->bindParam(':exchange_profit', $calculations['exchange_profit']);
        $stmt->bindParam(':total_profit', $calculations['total_profit']);
        $stmt->bindParam(':branch_id', $branch_id);
        $stmt->execute();
    }
    
    private function sendTransferNotification($transfer_id, $type) {
        // إرسال الإشعارات
        // يمكن تطوير هذه الدالة لاحقاً
    }
    
    private function logSystemEvent($event_type, $details, $customer_id = null, $transaction_id = null, $user_id = null) {
        $stmt = $this->db->prepare("
            INSERT INTO system_events (
                event_type, event_details, customer_id, transaction_id, user_id
            ) VALUES (
                :event_type, :details, :customer_id, :transaction_id, :user_id
            )
        ");
        $stmt->bindParam(':event_type', $event_type);
        $stmt->bindParam(':details', json_encode($details));
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }
}
?>
