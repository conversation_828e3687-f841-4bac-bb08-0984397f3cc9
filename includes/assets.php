<?php
/**
 * Trust Plus - Assets Management
 * إدارة الأصول (CSS, JS, Images)
 */

class Assets {
    private static $cssFiles = [];
    private static $jsFiles = [];
    private static $inlineCSS = [];
    private static $inlineJS = [];
    
    // مسارات الأصول
    const ASSETS_PATH = '../assets/';
    const CSS_PATH = '../assets/css/';
    const JS_PATH = '../assets/js/';
    
    // CDN URLs كبديل
    const CDN_BOOTSTRAP_CSS = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
    const CDN_BOOTSTRAP_JS = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
    const CDN_FONTAWESOME = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
    const CDN_CHART_JS = 'https://cdn.jsdelivr.net/npm/chart.js';
    const CDN_GOOGLE_FONTS = 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap';
    
    /**
     * إضافة ملف CSS
     */
    public static function addCSS($file, $priority = 10) {
        self::$cssFiles[] = [
            'file' => $file,
            'priority' => $priority,
            'local' => self::isLocalFile($file)
        ];
    }
    
    /**
     * إضافة ملف JavaScript
     */
    public static function addJS($file, $priority = 10, $defer = false, $async = false) {
        self::$jsFiles[] = [
            'file' => $file,
            'priority' => $priority,
            'defer' => $defer,
            'async' => $async,
            'local' => self::isLocalFile($file)
        ];
    }
    
    /**
     * إضافة CSS مضمن
     */
    public static function addInlineCSS($css, $priority = 10) {
        self::$inlineCSS[] = [
            'css' => $css,
            'priority' => $priority
        ];
    }
    
    /**
     * إضافة JavaScript مضمن
     */
    public static function addInlineJS($js, $priority = 10) {
        self::$inlineJS[] = [
            'js' => $js,
            'priority' => $priority
        ];
    }
    
    /**
     * تحميل الأصول الأساسية
     */
    public static function loadCore() {
        // Google Fonts
        self::addCSS(self::CDN_GOOGLE_FONTS, 1);
        
        // Bootstrap CSS
        if (file_exists(self::CSS_PATH . 'bootstrap.min.css')) {
            self::addCSS(self::CSS_PATH . 'bootstrap.min.css', 2);
        } else {
            self::addCSS(self::CDN_BOOTSTRAP_CSS, 2);
        }
        
        // Font Awesome
        if (file_exists(self::CSS_PATH . 'fontawesome.min.css')) {
            self::addCSS(self::CSS_PATH . 'fontawesome.min.css', 3);
        } else {
            self::addCSS(self::CDN_FONTAWESOME, 3);
        }
        
        // Main CSS
        self::addCSS(self::CSS_PATH . 'main.css', 4);
        
        // Bootstrap JS
        if (file_exists(self::JS_PATH . 'bootstrap.bundle.min.js')) {
            self::addJS(self::JS_PATH . 'bootstrap.bundle.min.js', 1, true);
        } else {
            self::addJS(self::CDN_BOOTSTRAP_JS, 1, true);
        }
        
        // Main JS
        self::addJS(self::JS_PATH . 'main.js', 2, true);
    }
    
    /**
     * تحميل أصول لوحة التحكم
     */
    public static function loadDashboard() {
        self::loadCore();
        
        // Dashboard CSS
        self::addCSS(self::CSS_PATH . 'dashboard.css', 5);
        
        // Chart.js
        if (file_exists(self::JS_PATH . 'chart.min.js')) {
            self::addJS(self::JS_PATH . 'chart.min.js', 3, true);
        } else {
            self::addJS(self::CDN_CHART_JS, 3, true);
        }
        
        // Dashboard JS
        self::addJS(self::JS_PATH . 'dashboard.js', 4, true);
    }
    
    /**
     * تحميل أصول التقارير
     */
    public static function loadReports() {
        self::loadCore();
        
        // Reports CSS
        self::addCSS(self::CSS_PATH . 'reports.css', 5);
        
        // Chart.js
        if (file_exists(self::JS_PATH . 'chart.min.js')) {
            self::addJS(self::JS_PATH . 'chart.min.js', 3, true);
        } else {
            self::addJS(self::CDN_CHART_JS, 3, true);
        }
        
        // Reports JS
        self::addJS(self::JS_PATH . 'reports.js', 4, true);
    }
    
    /**
     * تحميل أصول النماذج
     */
    public static function loadForms() {
        self::loadCore();
        
        // إضافة CSS خاص بالنماذج
        self::addInlineCSS('
            .form-step {
                display: none;
            }
            .form-step.active {
                display: block;
            }
            .step-indicator {
                margin-bottom: 2rem;
            }
        ');
        
        // إضافة JS خاص بالنماذج
        self::addInlineJS('
            // تفعيل التحقق من النماذج
            document.addEventListener("DOMContentLoaded", function() {
                const forms = document.querySelectorAll(".needs-validation");
                forms.forEach(function(form) {
                    form.addEventListener("submit", function(event) {
                        if (!form.checkValidity()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add("was-validated");
                    });
                });
            });
        ');
    }
    
    /**
     * إخراج CSS
     */
    public static function renderCSS() {
        // ترتيب الملفات حسب الأولوية
        usort(self::$cssFiles, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        $output = '';
        
        // ملفات CSS
        foreach (self::$cssFiles as $css) {
            $href = $css['local'] ? $css['file'] : $css['file'];
            $output .= '<link href="' . htmlspecialchars($href) . '" rel="stylesheet">' . "\n";
        }
        
        // CSS مضمن
        if (!empty(self::$inlineCSS)) {
            usort(self::$inlineCSS, function($a, $b) {
                return $a['priority'] - $b['priority'];
            });
            
            $output .= '<style>' . "\n";
            foreach (self::$inlineCSS as $css) {
                $output .= $css['css'] . "\n";
            }
            $output .= '</style>' . "\n";
        }
        
        return $output;
    }
    
    /**
     * إخراج JavaScript
     */
    public static function renderJS() {
        // ترتيب الملفات حسب الأولوية
        usort(self::$jsFiles, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        $output = '';
        
        // ملفات JavaScript
        foreach (self::$jsFiles as $js) {
            $src = $js['local'] ? $js['file'] : $js['file'];
            $attributes = '';
            
            if ($js['defer']) $attributes .= ' defer';
            if ($js['async']) $attributes .= ' async';
            
            $output .= '<script src="' . htmlspecialchars($src) . '"' . $attributes . '></script>' . "\n";
        }
        
        // JavaScript مضمن
        if (!empty(self::$inlineJS)) {
            usort(self::$inlineJS, function($a, $b) {
                return $a['priority'] - $b['priority'];
            });
            
            $output .= '<script>' . "\n";
            foreach (self::$inlineJS as $js) {
                $output .= $js['js'] . "\n";
            }
            $output .= '</script>' . "\n";
        }
        
        return $output;
    }
    
    /**
     * التحقق من كون الملف محلي
     */
    private static function isLocalFile($file) {
        return !filter_var($file, FILTER_VALIDATE_URL);
    }
    
    /**
     * إنشاء رابط للأصول
     */
    public static function asset($path) {
        return self::ASSETS_PATH . ltrim($path, '/');
    }
    
    /**
     * إنشاء رابط للصور
     */
    public static function image($path) {
        return self::asset('images/' . ltrim($path, '/'));
    }
    
    /**
     * إنشاء رابط لملف CSS
     */
    public static function css($path) {
        return self::asset('css/' . ltrim($path, '/'));
    }
    
    /**
     * إنشاء رابط لملف JavaScript
     */
    public static function js($path) {
        return self::asset('js/' . ltrim($path, '/'));
    }
    
    /**
     * تنظيف الذاكرة
     */
    public static function reset() {
        self::$cssFiles = [];
        self::$jsFiles = [];
        self::$inlineCSS = [];
        self::$inlineJS = [];
    }
    
    /**
     * إنشاء meta tags للصفحة
     */
    public static function renderMeta($title = 'Trust Plus', $description = '', $keywords = '') {
        $output = '';
        $output .= '<meta charset="UTF-8">' . "\n";
        $output .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">' . "\n";
        $output .= '<meta name="description" content="' . htmlspecialchars($description) . '">' . "\n";
        $output .= '<meta name="keywords" content="' . htmlspecialchars($keywords) . '">' . "\n";
        $output .= '<meta name="author" content="Trust Plus">' . "\n";
        $output .= '<title>' . htmlspecialchars($title) . '</title>' . "\n";
        
        // Favicon
        $output .= '<link rel="icon" type="image/x-icon" href="' . self::image('favicon.ico') . '">' . "\n";
        
        return $output;
    }
    
    /**
     * إنشاء preloader
     */
    public static function renderPreloader() {
        return '
        <div id="preloader" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                width: 50px;
                height: 50px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            "></div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        </div>
        <script>
            window.addEventListener("load", function() {
                const preloader = document.getElementById("preloader");
                if (preloader) {
                    preloader.style.opacity = "0";
                    setTimeout(() => preloader.remove(), 300);
                }
            });
        </script>
        ';
    }
}

// دوال مساعدة للاستخدام السريع
function asset($path) {
    return Assets::asset($path);
}

function css_asset($path) {
    return Assets::css($path);
}

function js_asset($path) {
    return Assets::js($path);
}

function image_asset($path) {
    return Assets::image($path);
}
?>
