<?php
/**
 * فئة إدارة العملاء مع نظام KYC/AML - Trust Plus System
 */

require_once 'auth.php';

class CustomerManager {
    private $db;
    private $auth;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->auth = new Auth();
    }
    
    /**
     * إضافة عميل جديد
     */
    public function addCustomer($data, $user_id) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من عدم وجود العميل مسبقاً
            $stmt = $this->db->prepare("
                SELECT id FROM customers 
                WHERE id_type = :id_type AND id_number = :id_number
            ");
            $stmt->bindParam(':id_type', $data['id_type']);
            $stmt->bindParam(':id_number', $data['id_number']);
            $stmt->execute();
            
            if ($stmt->fetch()) {
                throw new Exception('عميل بنفس نوع ورقم الهوية موجود مسبقاً');
            }
            
            // إدراج العميل الجديد
            $stmt = $this->db->prepare("
                INSERT INTO customers (
                    full_name, id_type, id_number, date_of_birth, address, 
                    phone, email, nationality, registered_by_user_id, registration_date
                ) VALUES (
                    :full_name, :id_type, :id_number, :date_of_birth, :address,
                    :phone, :email, :nationality, :user_id, CURDATE()
                )
            ");
            
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':id_type', $data['id_type']);
            $stmt->bindParam(':id_number', $data['id_number']);
            $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':nationality', $data['nationality']);
            $stmt->bindParam(':user_id', $user_id);
            
            $stmt->execute();
            $customer_id = $this->db->lastInsertId();
            
            // إنشاء فحص KYC أولي
            $this->createInitialKYCCheck($customer_id, $user_id);
            
            // تسجيل الحدث
            $this->logSystemEvent('customer_registered', [
                'customer_id' => $customer_id,
                'customer_name' => $data['full_name']
            ], $customer_id, null, $user_id);
            
            $this->db->commit();
            return ['success' => true, 'customer_id' => $customer_id, 'message' => 'تم إضافة العميل بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات العميل
     */
    public function updateCustomer($customer_id, $data, $user_id) {
        try {
            $this->db->beginTransaction();
            
            // جلب البيانات الحالية للمقارنة
            $current_data = $this->getCustomerById($customer_id);
            if (!$current_data) {
                throw new Exception('العميل غير موجود');
            }
            
            // تحديث البيانات
            $stmt = $this->db->prepare("
                UPDATE customers SET 
                    full_name = :full_name,
                    date_of_birth = :date_of_birth,
                    address = :address,
                    phone = :phone,
                    email = :email,
                    nationality = :nationality,
                    updated_at = NOW()
                WHERE id = :customer_id
            ");
            
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':nationality', $data['nationality']);
            $stmt->bindParam(':customer_id', $customer_id);
            
            $stmt->execute();
            
            // تسجيل التغييرات في تاريخ العميل
            $this->logCustomerChanges($customer_id, $current_data, $data, $user_id);
            
            $this->db->commit();
            return ['success' => true, 'message' => 'تم تحديث بيانات العميل بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * تحديث حالة KYC للعميل
     */
    public function updateKYCStatus($customer_id, $status, $user_id, $notes = '') {
        try {
            $this->db->beginTransaction();
            
            $old_status = $this->getCustomerById($customer_id)['kyc_status'];
            
            // تحديث حالة KYC
            $stmt = $this->db->prepare("
                UPDATE customers SET 
                    kyc_status = :status,
                    updated_at = NOW()
                WHERE id = :customer_id
            ");
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            // إنشاء فحص امتثال جديد
            $stmt = $this->db->prepare("
                INSERT INTO compliance_checks (
                    customer_id, check_type, check_result, 
                    performed_by_user_id, notes
                ) VALUES (
                    :customer_id, 'kyc_verification', :result, :user_id, :notes
                )
            ");
            
            $result = ($status === 'approved') ? 'pass' : (($status === 'rejected') ? 'fail' : 'pending');
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':result', $result);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notes', $notes);
            $stmt->execute();
            
            // تسجيل الحدث
            $event_type = ($status === 'approved') ? 'kyc_approved' : 'kyc_rejected';
            $this->logSystemEvent($event_type, [
                'old_status' => $old_status,
                'new_status' => $status,
                'notes' => $notes
            ], $customer_id, null, $user_id);
            
            $this->db->commit();
            return ['success' => true, 'message' => 'تم تحديث حالة التحقق بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * تحديث مستوى المخاطر للعميل
     */
    public function updateRiskLevel($customer_id, $risk_level, $user_id, $reason = '') {
        try {
            $this->db->beginTransaction();
            
            $old_risk = $this->getCustomerById($customer_id)['risk_level'];
            
            // تحديث مستوى المخاطر
            $stmt = $this->db->prepare("
                UPDATE customers SET 
                    risk_level = :risk_level,
                    updated_at = NOW()
                WHERE id = :customer_id
            ");
            $stmt->bindParam(':risk_level', $risk_level);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            // إنشاء تقييم مخاطر جديد
            $risk_score = $this->calculateRiskScore($risk_level);
            $stmt = $this->db->prepare("
                INSERT INTO compliance_checks (
                    customer_id, check_type, check_result, risk_score,
                    performed_by_user_id, notes
                ) VALUES (
                    :customer_id, 'risk_assessment', 'pass', :risk_score, :user_id, :notes
                )
            ");
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':risk_score', $risk_score);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notes', $reason);
            $stmt->execute();
            
            // تسجيل الحدث
            $this->logSystemEvent('risk_level_changed', [
                'old_risk' => $old_risk,
                'new_risk' => $risk_level,
                'reason' => $reason
            ], $customer_id, null, $user_id);
            
            $this->db->commit();
            return ['success' => true, 'message' => 'تم تحديث مستوى المخاطر بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * إضافة/إزالة عميل من القائمة السوداء
     */
    public function updateBlacklistStatus($customer_id, $blacklisted, $reason, $user_id) {
        try {
            $this->db->beginTransaction();
            
            $stmt = $this->db->prepare("
                UPDATE customers SET 
                    blacklisted = :blacklisted,
                    blacklisted_reason = :reason,
                    updated_at = NOW()
                WHERE id = :customer_id
            ");
            $stmt->bindParam(':blacklisted', $blacklisted);
            $stmt->bindParam(':reason', $blacklisted ? $reason : null);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            // تسجيل الحدث
            $event_type = $blacklisted ? 'blacklist_added' : 'blacklist_removed';
            $this->logSystemEvent($event_type, [
                'reason' => $reason
            ], $customer_id, null, $user_id, 'high');
            
            $this->db->commit();
            
            $message = $blacklisted ? 'تم إضافة العميل للقائمة السوداء' : 'تم إزالة العميل من القائمة السوداء';
            return ['success' => true, 'message' => $message];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * جلب بيانات عميل بالمعرف
     */
    public function getCustomerById($customer_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT c.*, u.full_name as registered_by_name, b.name as branch_name
                FROM customers c
                LEFT JOIN users u ON c.registered_by_user_id = u.id
                LEFT JOIN branches b ON u.branch_id = b.id
                WHERE c.id = :customer_id
            ");
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * البحث في العملاء
     */
    public function searchCustomers($search_term, $filters = [], $limit = 50, $offset = 0) {
        try {
            $where_conditions = [];
            $params = [];
            
            // البحث النصي
            if (!empty($search_term)) {
                $where_conditions[] = "(c.full_name LIKE :search OR c.id_number LIKE :search OR c.phone LIKE :search OR c.email LIKE :search)";
                $params[':search'] = "%$search_term%";
            }
            
            // فلاتر إضافية
            if (!empty($filters['kyc_status'])) {
                $where_conditions[] = "c.kyc_status = :kyc_status";
                $params[':kyc_status'] = $filters['kyc_status'];
            }
            
            if (!empty($filters['risk_level'])) {
                $where_conditions[] = "c.risk_level = :risk_level";
                $params[':risk_level'] = $filters['risk_level'];
            }
            
            if (isset($filters['blacklisted'])) {
                $where_conditions[] = "c.blacklisted = :blacklisted";
                $params[':blacklisted'] = $filters['blacklisted'];
            }
            
            $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);
            
            $stmt = $this->db->prepare("
                SELECT c.*, u.full_name as registered_by_name
                FROM customers c
                LEFT JOIN users u ON c.registered_by_user_id = u.id
                $where_clause
                ORDER BY c.created_at DESC
                LIMIT :limit OFFSET :offset
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * إنشاء فحص KYC أولي
     */
    private function createInitialKYCCheck($customer_id, $user_id) {
        $stmt = $this->db->prepare("
            INSERT INTO compliance_checks (
                customer_id, check_type, check_result, 
                performed_by_user_id, notes
            ) VALUES (
                :customer_id, 'kyc_verification', 'pending', :user_id, 'فحص KYC أولي عند التسجيل'
            )
        ");
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }
    
    /**
     * تسجيل التغييرات في تاريخ العميل
     */
    private function logCustomerChanges($customer_id, $old_data, $new_data, $user_id) {
        $fields_to_track = ['full_name', 'date_of_birth', 'address', 'phone', 'email', 'nationality'];
        
        foreach ($fields_to_track as $field) {
            if (isset($old_data[$field]) && isset($new_data[$field]) && $old_data[$field] !== $new_data[$field]) {
                $stmt = $this->db->prepare("
                    INSERT INTO customer_history (
                        customer_id, field_name, old_value, new_value, changed_by_user_id
                    ) VALUES (
                        :customer_id, :field_name, :old_value, :new_value, :user_id
                    )
                ");
                $stmt->bindParam(':customer_id', $customer_id);
                $stmt->bindParam(':field_name', $field);
                $stmt->bindParam(':old_value', $old_data[$field]);
                $stmt->bindParam(':new_value', $new_data[$field]);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
            }
        }
    }
    
    /**
     * تسجيل أحداث النظام
     */
    private function logSystemEvent($event_type, $details, $customer_id = null, $transaction_id = null, $user_id = null, $severity = 'medium') {
        $stmt = $this->db->prepare("
            INSERT INTO system_events (
                event_type, event_details, customer_id, transaction_id, user_id, severity
            ) VALUES (
                :event_type, :details, :customer_id, :transaction_id, :user_id, :severity
            )
        ");
        $details_json = json_encode($details);
        $stmt->bindParam(':event_type', $event_type);
        $stmt->bindParam(':details', $details_json);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':severity', $severity);
        $stmt->execute();
    }
    
    /**
     * حساب نقاط المخاطر
     */
    private function calculateRiskScore($risk_level) {
        switch ($risk_level) {
            case 'low': return 25;
            case 'medium': return 50;
            case 'high': return 75;
            default: return 0;
        }
    }
}
?>
