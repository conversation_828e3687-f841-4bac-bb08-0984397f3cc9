<?php
/**
 * نظام المصادقة والحماية - Trust Plus System
 */

// تضمين ملف التكوين
if (file_exists(__DIR__ . '/../config.php')) {
    require_once __DIR__ . '/../config.php';
}

// تضمين ملف قاعدة البيانات
if (file_exists(__DIR__ . '/database.php')) {
    require_once __DIR__ . '/database.php';
} elseif (file_exists(__DIR__ . '/../config/database.php')) {
    require_once __DIR__ . '/../config/database.php';
}

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            // التحقق من محاولات تسجيل الدخول الفاشلة
            $stmt = $this->db->prepare("
                SELECT id, username, email, password_hash, full_name, role_id, branch_id,
                       is_active, failed_login_attempts, locked_until
                FROM users
                WHERE (username = :username OR email = :email) AND is_active = 1
            ");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $username);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من قفل الحساب
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                return ['success' => false, 'message' => 'الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً'];
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password_hash'])) {
                $this->incrementFailedAttempts($user['id']);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // تسجيل دخول ناجح
            $this->resetFailedAttempts($user['id']);
            $this->updateLastLogin($user['id']);

            // إنشاء جلسة المستخدم
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['role_id'] = $user['role_id'];
            $_SESSION['branch_id'] = $user['branch_id'];
            $_SESSION['login_time'] = time();
            
            // تسجيل عملية تسجيل الدخول في سجل التدقيق
            $this->logActivity($user['id'], 'login', 'user', $user['id']);
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout($reason = 'manual') {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $user_id = $_SESSION['user_id'] ?? null;
        $username = $_SESSION['username'] ?? 'غير محدد';

        if ($user_id) {
            $this->logActivity($user_id, 'logout', 'user', $user_id, "تسجيل خروج: $reason");
        }

        // حفظ معلومات الجلسة قبل الحذف للرسائل
        $session_info = [
            'user_id' => $user_id,
            'username' => $username,
            'reason' => $reason,
            'logout_time' => date('Y-m-d H:i:s')
        ];

        // مسح جميع متغيرات الجلسة
        $_SESSION = array();

        // حذف كوكي الجلسة إذا كان موجوداً
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // تدمير الجلسة
        session_destroy();

        return $session_info;
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public function checkSession($update_activity = true) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        // التحقق من انتهاء مهلة الجلسة
        $session_timeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600; // ساعة واحدة افتراضياً
        $current_time = time();
        $login_time = $_SESSION['login_time'] ?? $current_time;
        $last_activity = $_SESSION['last_activity'] ?? $login_time;

        // التحقق من انتهاء الجلسة بناءً على آخر نشاط
        if (($current_time - $last_activity) > $session_timeout) {
            $this->logout('session_timeout');
            return false;
        }

        // التحقق من الحد الأقصى لمدة الجلسة (24 ساعة)
        $max_session_time = defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME : 86400; // 24 ساعة
        if (($current_time - $login_time) > $max_session_time) {
            $this->logout('max_session_exceeded');
            return false;
        }

        // تحديث وقت آخر نشاط
        if ($update_activity) {
            $_SESSION['last_activity'] = $current_time;
        }

        return true;
    }

    /**
     * التحقق من صحة الجلسة مع إرجاع سبب الفشل
     */
    public function checkSessionWithReason() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['user_id'])) {
            return ['valid' => false, 'reason' => 'no_session', 'message' => 'لا توجد جلسة نشطة'];
        }

        $session_timeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600;
        $current_time = time();
        $login_time = $_SESSION['login_time'] ?? $current_time;
        $last_activity = $_SESSION['last_activity'] ?? $login_time;

        // التحقق من انتهاء الجلسة بناءً على آخر نشاط
        if (($current_time - $last_activity) > $session_timeout) {
            $this->logout('session_timeout');
            return [
                'valid' => false,
                'reason' => 'session_timeout',
                'message' => 'انتهت صلاحية الجلسة بسبب عدم النشاط'
            ];
        }

        // التحقق من الحد الأقصى لمدة الجلسة
        $max_session_time = defined('MAX_SESSION_TIME') ? MAX_SESSION_TIME : 86400;
        if (($current_time - $login_time) > $max_session_time) {
            $this->logout('max_session_exceeded');
            return [
                'valid' => false,
                'reason' => 'max_session_exceeded',
                'message' => 'تم تجاوز الحد الأقصى لمدة الجلسة'
            ];
        }

        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = $current_time;

        return ['valid' => true, 'reason' => null, 'message' => 'الجلسة صحيحة'];
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public function hasPermission($permission) {
        if (!$this->checkSession()) {
            return false;
        }

        // للتبسيط، نعطي صلاحيات كاملة للمدير (role_id = 1)
        if (isset($_SESSION['role_id']) && $_SESSION['role_id'] == 1) {
            return true;
        }

        // يمكن إضافة منطق أكثر تعقيداً هنا لاحقاً
        return true; // مؤقتاً، نعطي صلاحيات لجميع المستخدمين
    }
    
    /**
     * الحصول على معلومات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->checkSession()) {
            return null;
        }

        try {
            $stmt = $this->db->prepare("
                SELECT * FROM users WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->execute();

            $user = $stmt->fetch();

            // إضافة معلومات إضافية من الجلسة
            if ($user) {
                $user['role_name'] = 'مدير النظام'; // مؤقتاً
                $user['branch_name'] = 'الفرع الرئيسي'; // مؤقتاً
            }

            return $user;

        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * زيادة عدد محاولات تسجيل الدخول الفاشلة
     */
    private function incrementFailedAttempts($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET failed_login_attempts = failed_login_attempts + 1,
                    locked_until = CASE 
                        WHEN failed_login_attempts + 1 >= :max_attempts 
                        THEN DATE_ADD(NOW(), INTERVAL 30 MINUTE)
                        ELSE locked_until 
                    END
                WHERE id = :user_id
            ");
            $max_attempts = defined('MAX_LOGIN_ATTEMPTS') ? MAX_LOGIN_ATTEMPTS : 5;
            $stmt->bindParam(':max_attempts', $max_attempts);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    private function resetFailedAttempts($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET failed_login_attempts = 0, locked_until = NULL
                WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * تحديث وقت آخر تسجيل دخول
     */
    private function updateLastLogin($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET last_login = NOW()
                WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * تسجيل النشاطات في سجل التدقيق
     */
    private function logActivity($user_id, $action, $entity_type, $entity_id, $old_value = null, $new_value = null) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_value, new_value, ip_address, user_agent)
                VALUES (:user_id, :action, :entity_type, :entity_id, :old_value, :new_value, :ip_address, :user_agent)
            ");

            // تحضير القيم
            $old_value_json = $old_value ? json_encode($old_value) : null;
            $new_value_json = $new_value ? json_encode($new_value) : null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':entity_id', $entity_id);
            $stmt->bindParam(':old_value', $old_value_json);
            $stmt->bindParam(':new_value', $new_value_json);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':user_agent', $user_agent);

            $stmt->execute();

        } catch (Exception $e) {
            // تسجيل الخطأ - تجاهل أخطاء سجل التدقيق لعدم تعطيل النظام
            error_log("Audit log error: " . $e->getMessage());
        }
    }
}
?>
