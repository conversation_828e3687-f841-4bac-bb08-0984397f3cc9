<?php
/**
 * نظام المصادقة والحماية - Trust Plus System
 */

// تضمين ملف التكوين
if (file_exists(__DIR__ . '/../config.php')) {
    require_once __DIR__ . '/../config.php';
}

// تضمين ملف قاعدة البيانات
if (file_exists(__DIR__ . '/database.php')) {
    require_once __DIR__ . '/database.php';
} elseif (file_exists(__DIR__ . '/../config/database.php')) {
    require_once __DIR__ . '/../config/database.php';
}

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            // التحقق من محاولات تسجيل الدخول الفاشلة
            $stmt = $this->db->prepare("
                SELECT id, username, email, password_hash, full_name, role_id, branch_id,
                       is_active, failed_login_attempts, locked_until
                FROM users
                WHERE (username = :username OR email = :email) AND is_active = 1
            ");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $username);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من قفل الحساب
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                return ['success' => false, 'message' => 'الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً'];
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password_hash'])) {
                $this->incrementFailedAttempts($user['id']);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // تسجيل دخول ناجح
            $this->resetFailedAttempts($user['id']);
            $this->updateLastLogin($user['id']);
            
            // إنشاء جلسة المستخدم
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['role_id'] = $user['role_id'];
            $_SESSION['branch_id'] = $user['branch_id'];
            $_SESSION['login_time'] = time();
            
            // تسجيل عملية تسجيل الدخول في سجل التدقيق
            $this->logActivity($user['id'], 'login', 'user', $user['id']);
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'user', $_SESSION['user_id']);
        }
        
        session_destroy();
        return true;
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public function checkSession() {
        session_start();
        
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // التحقق من انتهاء مهلة الجلسة
        $session_timeout = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 3600; // ساعة واحدة افتراضياً
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > $session_timeout) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public function hasPermission($permission) {
        if (!$this->checkSession()) {
            return false;
        }

        // للتبسيط، نعطي صلاحيات كاملة للمدير (role_id = 1)
        if (isset($_SESSION['role_id']) && $_SESSION['role_id'] == 1) {
            return true;
        }

        // يمكن إضافة منطق أكثر تعقيداً هنا لاحقاً
        return true; // مؤقتاً، نعطي صلاحيات لجميع المستخدمين
    }
    
    /**
     * الحصول على معلومات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->checkSession()) {
            return null;
        }

        try {
            $stmt = $this->db->prepare("
                SELECT * FROM users WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->execute();

            $user = $stmt->fetch();

            // إضافة معلومات إضافية من الجلسة
            if ($user) {
                $user['role_name'] = 'مدير النظام'; // مؤقتاً
                $user['branch_name'] = 'الفرع الرئيسي'; // مؤقتاً
            }

            return $user;

        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * زيادة عدد محاولات تسجيل الدخول الفاشلة
     */
    private function incrementFailedAttempts($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET failed_login_attempts = failed_login_attempts + 1,
                    locked_until = CASE 
                        WHEN failed_login_attempts + 1 >= :max_attempts 
                        THEN DATE_ADD(NOW(), INTERVAL 30 MINUTE)
                        ELSE locked_until 
                    END
                WHERE id = :user_id
            ");
            $max_attempts = defined('MAX_LOGIN_ATTEMPTS') ? MAX_LOGIN_ATTEMPTS : 5;
            $stmt->bindParam(':max_attempts', $max_attempts);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    private function resetFailedAttempts($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET failed_login_attempts = 0, locked_until = NULL
                WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * تحديث وقت آخر تسجيل دخول
     */
    private function updateLastLogin($user_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET last_login = NOW()
                WHERE id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
        } catch (Exception $e) {
            // تسجيل الخطأ
        }
    }
    
    /**
     * تسجيل النشاطات في سجل التدقيق
     */
    private function logActivity($user_id, $action, $entity_type, $entity_id, $old_value = null, $new_value = null) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_value, new_value, ip_address, user_agent)
                VALUES (:user_id, :action, :entity_type, :entity_id, :old_value, :new_value, :ip_address, :user_agent)
            ");

            // تحضير القيم
            $old_value_json = $old_value ? json_encode($old_value) : null;
            $new_value_json = $new_value ? json_encode($new_value) : null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':entity_id', $entity_id);
            $stmt->bindParam(':old_value', $old_value_json);
            $stmt->bindParam(':new_value', $new_value_json);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':user_agent', $user_agent);

            $stmt->execute();

        } catch (Exception $e) {
            // تسجيل الخطأ - تجاهل أخطاء سجل التدقيق لعدم تعطيل النظام
            error_log("Audit log error: " . $e->getMessage());
        }
    }
}
?>
