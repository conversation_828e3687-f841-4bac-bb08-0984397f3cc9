<?php
/**
 * Trust Plus - Header Template
 * قالب الرأس المشترك
 */

require_once __DIR__ . '/assets.php';

// تحديد نوع الصفحة لتحميل الأصول المناسبة
$page_type = $page_type ?? 'default';
$page_title = $page_title ?? 'Trust Plus';
$page_description = $page_description ?? 'نظام إدارة الصرافة والتحويلات المالية';
$page_keywords = $page_keywords ?? 'صرافة, تحويلات مالية, نظام إدارة, Trust Plus';

// تحميل الأصول حسب نوع الصفحة
switch ($page_type) {
    case 'dashboard':
        Assets::loadDashboard();
        break;
    case 'reports':
        Assets::loadReports();
        break;
    case 'forms':
        Assets::loadForms();
        break;
    default:
        Assets::loadCore();
        break;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <?php echo Assets::renderMeta($page_title, $page_description, $page_keywords); ?>
    
    <?php echo Assets::renderCSS(); ?>
    
    <!-- إعدادات إضافية للصفحة -->
    <?php if (isset($additional_head)): ?>
        <?php echo $additional_head; ?>
    <?php endif; ?>
</head>
<body>
    <?php if (!isset($hide_preloader) || !$hide_preloader): ?>
        <?php echo Assets::renderPreloader(); ?>
    <?php endif; ?>
    
    <!-- بداية المحتوى -->
    <div class="app-container">
        <?php if (!isset($hide_sidebar) || !$hide_sidebar): ?>
            <!-- الشريط الجانبي -->
            <?php include __DIR__ . '/sidebar.php'; ?>
        <?php endif; ?>
        
        <!-- المحتوى الرئيسي -->
        <div class="main-content<?php echo (!isset($hide_sidebar) || !$hide_sidebar) ? '' : ' no-sidebar'; ?>">
            <?php if (!isset($hide_navbar) || !$hide_navbar): ?>
                <!-- شريط التنقل العلوي -->
                <?php include __DIR__ . '/navbar.php'; ?>
            <?php endif; ?>
            
            <!-- محتوى الصفحة -->
            <div class="page-content">
                <?php if (isset($show_breadcrumb) && $show_breadcrumb): ?>
                    <!-- مسار التنقل -->
                    <?php include __DIR__ . '/breadcrumb.php'; ?>
                <?php endif; ?>
                
                <?php if (isset($page_header) && $page_header): ?>
                    <!-- رأس الصفحة -->
                    <div class="page-header">
                        <div class="container-fluid">
                            <div class="row align-items-center">
                                <div class="col">
                                    <?php if (isset($page_icon)): ?>
                                        <i class="<?php echo htmlspecialchars($page_icon); ?> me-2"></i>
                                    <?php endif; ?>
                                    <h1 class="page-title d-inline"><?php echo htmlspecialchars($page_header); ?></h1>
                                    <?php if (isset($page_subtitle)): ?>
                                        <p class="page-subtitle text-muted"><?php echo htmlspecialchars($page_subtitle); ?></p>
                                    <?php endif; ?>
                                </div>
                                <?php if (isset($page_actions)): ?>
                                    <div class="col-auto">
                                        <div class="page-actions">
                                            <?php echo $page_actions; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- رسائل النظام -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="container-fluid">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['success_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="container-fluid">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['error_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['warning_message'])): ?>
                    <div class="container-fluid">
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['warning_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    <?php unset($_SESSION['warning_message']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['info_message'])): ?>
                    <div class="container-fluid">
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['info_message']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    <?php unset($_SESSION['info_message']); ?>
                <?php endif; ?>

                <!-- بداية محتوى الصفحة الفعلي -->

                <?php
                // إضافة مراقب الجلسة للصفحات المحمية
                if (isset($auth) && $auth->checkSession(false)): ?>
                    <script>
                        // تعيين علامة للصفحات المحمية
                        document.body.classList.add('logged-in');
                        document.body.setAttribute('data-session-monitor', 'true');
                    </script>
                <?php endif; ?>
