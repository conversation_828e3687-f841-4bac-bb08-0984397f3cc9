/* Trust Plus - Main CSS File */
/* ملف CSS الرئيسي لنظام Trust Plus */

/* Google Fonts - Cairo */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات العامة */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --secondary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
    color: var(--dark-color);
}

/* البطاقات */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
}

/* الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-gradient);
    border: none;
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
}

.btn-info {
    background: var(--info-gradient);
    border: none;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
}

/* النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

/* الشريط الجانبي */
.sidebar {
    background: var(--primary-gradient);
    min-height: 100vh;
    transition: var(--transition);
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar .nav {
    padding: 1rem 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    margin: 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    text-decoration: none;
    border: none;
    background: none;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-right: 3px solid white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

.sidebar-text {
    transition: var(--transition);
    white-space: nowrap;
}

.sidebar.collapsed .sidebar-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 1rem 0;
}

.sidebar-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 280px;
    transition: var(--transition);
    min-height: 100vh;
    padding: 2rem;
    background: #f8f9fa;
}

.main-content.expanded {
    margin-right: 80px;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.bg-primary-gradient {
    background: var(--primary-gradient);
}

.bg-success-gradient {
    background: var(--success-gradient);
}

.bg-warning-gradient {
    background: var(--warning-gradient);
}

.bg-info-gradient {
    background: var(--info-gradient);
}

.bg-secondary-gradient {
    background: var(--secondary-gradient);
}

.bg-danger-gradient {
    background: var(--danger-gradient);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

/* الشارات */
.badge {
    border-radius: 25px;
    font-weight: 600;
    padding: 0.5rem 1rem;
}

/* التقدم */
.progress {
    border-radius: 25px;
    height: 8px;
}

.progress-bar {
    border-radius: 25px;
}

/* الخطوات */
.step {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 1rem;
    transition: var(--transition);
}

.step.active .step-circle {
    background: var(--primary-color);
    color: white;
}

.step.completed .step-circle {
    background: var(--success-color);
    color: white;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .main-content {
        margin-right: 280px;
        padding: 1.5rem;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(100%);
        width: 320px;
        box-shadow: -5px 0 15px rgba(0,0,0,0.2);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .main-content.expanded {
        margin-right: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .sidebar-backdrop.show {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(100%);
    }

    .main-content {
        padding: 1rem 0.5rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

/* طباعة */
@media print {
    .sidebar,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    body {
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* تخصيصات إضافية */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: var(--primary-gradient) 1;
}

.shadow-lg {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.rounded-lg {
    border-radius: var(--border-radius) !important;
}

/* حالات التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* الشريط العلوي (Navbar) */
.navbar {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.navbar .btn {
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
    border: 1px solid #dee2e6;
}

.navbar .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.navbar .btn-outline-secondary {
    color: #6c757d;
    border-color: #dee2e6;
}

.navbar .btn-outline-secondary:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.navbar .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.navbar .btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: white;
}

/* أيقونات الشريط العلوي */
.navbar .fas, .navbar .far {
    font-size: 1.1rem;
    transition: var(--transition);
}

.navbar .btn:hover .fas,
.navbar .btn:hover .far {
    transform: scale(1.1);
}

/* شارة الإشعارات */
.navbar .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.4rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* قوائم الشريط العلوي المنسدلة */
.navbar .dropdown-menu {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.navbar .dropdown-item {
    padding: 0.75rem 1rem;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
}

.navbar .dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.navbar .dropdown-header {
    font-weight: 600;
    color: var(--dark-color);
    padding: 0.75rem 1rem 0.5rem;
}

.navbar .dropdown-divider {
    margin: 0.5rem 0;
    border-color: #e9ecef;
}

/* معلومات المستخدم */
.user-avatar {
    transition: var(--transition);
}

.navbar .btn:hover .user-avatar {
    transform: scale(1.05);
}

.user-info {
    line-height: 1.2;
}

.user-info .fw-bold {
    font-size: 0.9rem;
}

.user-info .text-muted {
    font-size: 0.75rem;
}

/* البحث السريع */
.navbar .form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
}

.navbar .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* زر تبديل الشريط الجانبي */
.sidebar-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .navbar .d-none.d-md-inline {
        display: none !important;
    }

    .navbar .btn {
        padding: 0.4rem 0.6rem;
    }

    .navbar .dropdown-menu {
        min-width: 250px !important;
    }

    .user-info {
        display: none !important;
    }
}

/* تأثيرات خاصة للأيقونات */
.navbar .fas.fa-bell {
    animation: swing 3s ease-in-out infinite;
}

@keyframes swing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: rotate(10deg); }
    20%, 40%, 60%, 80% { transform: rotate(-10deg); }
}

.navbar .fas.fa-search:hover {
    animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.navbar .fas.fa-cog:hover {
    animation: rotate 0.5s ease-in-out;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(180deg); }
}
