/* Trust Plus - Dashboard Specific CSS */
/* ملف CSS خاص بلوحة التحكم */

/* إعادة تعريف المتغيرات للتأكد من توفرها */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --secondary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);

    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}

/* حاوية لوحة التحكم */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* رأس الصفحة */
.page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
}

.page-title {
    color: var(--dark-color);
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
}

.page-title i {
    color: var(--primary-color);
    margin-left: 0.75rem;
    font-size: 1.2em;
}

.page-subtitle {
    color: #6c757d;
    margin: 0.5rem 0 0 0;
    font-size: 0.95rem;
}

/* بطاقات الإحصائيات المحسنة */
.stats-card,
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
}

.stats-card::before,
.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.stats-card:hover,
.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

/* أيقونات الإحصائيات */
.stats-icon,
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.stats-icon::before,
.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
    border-radius: inherit;
}

.stats-icon i,
.stat-icon i {
    position: relative;
    z-index: 1;
}

/* قيم الإحصائيات */
.stats-value,
.stat-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1;
    font-family: 'Cairo', sans-serif;
}

.stats-label,
.stat-label {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    line-height: 1.2;
    font-weight: 500;
}

.stats-change,
.stat-change {
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stats-change.positive,
.stat-change.positive {
    color: var(--success-color);
}

.stats-change.negative,
.stat-change.negative {
    color: var(--danger-color);
}

.stats-change i,
.stat-change i {
    font-size: 0.8rem;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* الإجراءات السريعة المحسنة */
.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    border: 1px solid rgba(0,0,0,0.05);
}

.quick-actions h5 {
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-actions h5::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* شبكة الإجراءات */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
}

/* أزرار الإجراءات */
.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.75rem 1.25rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    min-height: 140px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: 0;
}

.action-btn:hover::before {
    opacity: 0.05;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
    text-decoration: none;
}

.action-btn i {
    font-size: 2.25rem;
    margin-bottom: 1rem;
    display: block;
    position: relative;
    z-index: 1;
    transition: var(--transition);
}

.action-btn:hover i {
    transform: scale(1.1);
}

.action-btn span {
    font-weight: 600;
    font-size: 0.95rem;
    line-height: 1.3;
    position: relative;
    z-index: 1;
}

/* الأنشطة الأخيرة */
.recent-activities {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
}

.chart-canvas {
    position: relative;
    height: 300px;
}

/* التنبيهات */
.alerts-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-item.warning {
    background: #fff3cd;
    border-left-color: var(--warning-color);
}

.alert-item.danger {
    background: #f8d7da;
    border-left-color: var(--danger-color);
}

.alert-item.info {
    background: #d1ecf1;
    border-left-color: var(--info-color);
}

.alert-icon {
    margin-left: 1rem;
    font-size: 1.25rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-size: 0.9rem;
    color: #6c757d;
}

/* التقويم */
.calendar-widget {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.calendar-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-nav {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1.25rem;
    cursor: pointer;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-day:hover {
    background: rgba(102, 126, 234, 0.1);
}

.calendar-day.today {
    background: var(--primary-color);
    color: white;
}

.calendar-day.has-event {
    background: var(--warning-color);
    color: white;
}

/* الطقس */
.weather-widget {
    background: var(--info-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.weather-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.weather-temp {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.weather-desc {
    opacity: 0.9;
}

/* التصميم المتجاوب للوحة التحكم */
@media (max-width: 992px) {
    .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stat-card {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
    }
    
    .activity-icon {
        margin: 0 0 0.5rem 0;
    }
}
