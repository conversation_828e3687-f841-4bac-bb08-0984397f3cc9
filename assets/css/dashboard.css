/* Trust Plus - Dashboard Specific CSS */
/* ملف CSS خاص بلوحة التحكم */

/* لوحة التحكم الرئيسية */
.dashboard-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.dashboard-welcome {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

/* بطاقات الإحصائيات السريعة */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* الإجراءات السريعة */
.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.quick-actions h5 {
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* الأنشطة الأخيرة */
.recent-activities {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
}

.chart-canvas {
    position: relative;
    height: 300px;
}

/* التنبيهات */
.alerts-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-item.warning {
    background: #fff3cd;
    border-left-color: var(--warning-color);
}

.alert-item.danger {
    background: #f8d7da;
    border-left-color: var(--danger-color);
}

.alert-item.info {
    background: #d1ecf1;
    border-left-color: var(--info-color);
}

.alert-icon {
    margin-left: 1rem;
    font-size: 1.25rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-size: 0.9rem;
    color: #6c757d;
}

/* التقويم */
.calendar-widget {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.calendar-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-nav {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1.25rem;
    cursor: pointer;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-day:hover {
    background: rgba(102, 126, 234, 0.1);
}

.calendar-day.today {
    background: var(--primary-color);
    color: white;
}

.calendar-day.has-event {
    background: var(--warning-color);
    color: white;
}

/* الطقس */
.weather-widget {
    background: var(--info-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.weather-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.weather-temp {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.weather-desc {
    opacity: 0.9;
}

/* التصميم المتجاوب للوحة التحكم */
@media (max-width: 992px) {
    .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stat-card {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
    }
    
    .activity-icon {
        margin: 0 0 0.5rem 0;
    }
}
