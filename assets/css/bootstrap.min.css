/* Bootstrap CSS Placeholder - يجب استبداله بالملف الفعلي */
.container-fluid { width: 100%; padding: 0 15px; }
.row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
.col, .col-12, .col-md-6, .col-lg-4 { padding: 0 15px; }
.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-md-6 { flex: 0 0 50%; max-width: 50%; }
.col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.card { border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 1rem; }
.card-header { padding: 12px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
.card-body { padding: 20px; }
.alert { padding: 12px 20px; border-radius: 4px; margin-bottom: 1rem; }
.alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
