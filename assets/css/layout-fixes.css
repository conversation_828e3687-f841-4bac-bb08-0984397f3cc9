/* Trust Plus - Layout Fixes CSS */
/* ملف CSS لإصلاح مشاكل التخطيط */

/* إعادة تعريف المتغيرات للتأكد من توفرها */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --secondary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* إصلاح التخطيط العام */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* إصلاح فئات التدرجات */
.bg-primary-gradient {
    background: var(--primary-gradient) !important;
}

.bg-success-gradient {
    background: var(--success-gradient) !important;
}

.bg-warning-gradient {
    background: var(--warning-gradient) !important;
}

.bg-info-gradient {
    background: var(--info-gradient) !important;
}

.bg-secondary-gradient {
    background: var(--secondary-gradient) !important;
}

.bg-danger-gradient {
    background: var(--danger-gradient) !important;
}

/* إصلاح الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 280px;
    background: var(--primary-gradient);
    z-index: 1000;
    overflow-y: auto;
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar .nav-item {
    margin: 0;
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-right: 3px solid white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

/* إصلاح المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
    background: #f8f9fa;
    transition: margin-right 0.3s ease;
}

.main-content.no-sidebar {
    margin-right: 0;
}

/* إصلاح محتوى الصفحة */
.page-content {
    padding: 2rem;
}

/* إصلاح بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* إصلاح الإجراءات السريعة */
.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    min-height: 120px;
    text-align: center;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
    text-decoration: none;
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    display: block;
}

.action-btn span {
    font-weight: 600;
    font-size: 0.9rem;
    line-height: 1.2;
}

/* إصلاح الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.chart-canvas {
    position: relative;
    height: 300px;
    width: 100%;
}

/* إصلاح الأنشطة الأخيرة */
.recent-activities {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    height: fit-content;
}

.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* إصلاح التصميم المتجاوب */
@media (max-width: 1200px) {
    .main-content {
        padding: 1.5rem;
    }
    
    .page-content {
        padding: 1.5rem;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(100%);
        width: 320px;
        box-shadow: -5px 0 15px rgba(0,0,0,0.2);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: flex !important;
    }
    
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
    }
    
    .page-content {
        padding: 1rem;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        text-align: center;
    }
    
    .chart-canvas {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .page-content {
        padding: 0.5rem;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }
    
    .activity-icon {
        margin: 0 0 0.5rem 0;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .chart-canvas {
        height: 200px;
    }
}
