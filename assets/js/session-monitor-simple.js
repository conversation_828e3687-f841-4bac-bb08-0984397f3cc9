/**
 * Trust Plus - Simple Session Monitor JavaScript
 * ملف JavaScript لمراقبة الجلسة بشكل مبسط
 */

// إعدادات مراقب الجلسة
const SessionMonitor = {
    // إعدادات افتراضية
    settings: {
        checkInterval: 300000, // 5 دقائق
        warningTime: 120000,   // تحذير قبل دقيقتين
        enabled: true,
        showWarnings: true
    },
    
    // متغيرات الحالة
    state: {
        isActive: false,
        lastActivity: Date.now(),
        warningShown: false,
        checkTimer: null
    },
    
    // تهيئة المراقب
    init: function() {
        if (!this.settings.enabled) return;
        
        this.bindEvents();
        this.startMonitoring();
        console.log('Session Monitor initialized');
    },
    
    // ربط الأحداث
    bindEvents: function() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, true);
        });
        
        // مراقبة تغيير التبويب
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.updateActivity();
            }
        });
        
        // مراقبة التركيز على النافذة
        window.addEventListener('focus', () => {
            this.updateActivity();
        });
    },
    
    // تحديث النشاط
    updateActivity: function() {
        this.state.lastActivity = Date.now();
        this.state.warningShown = false;
        
        if (!this.state.isActive) {
            this.state.isActive = true;
            this.startMonitoring();
        }
    },
    
    // بدء المراقبة
    startMonitoring: function() {
        if (this.state.checkTimer) {
            clearInterval(this.state.checkTimer);
        }
        
        this.state.checkTimer = setInterval(() => {
            this.checkSession();
        }, 60000); // فحص كل دقيقة
        
        this.state.isActive = true;
    },
    
    // إيقاف المراقبة
    stopMonitoring: function() {
        if (this.state.checkTimer) {
            clearInterval(this.state.checkTimer);
            this.state.checkTimer = null;
        }
        this.state.isActive = false;
    },
    
    // فحص الجلسة
    checkSession: function() {
        const now = Date.now();
        const timeSinceActivity = now - this.state.lastActivity;
        
        // إذا تجاوز الوقت المحدد
        if (timeSinceActivity >= this.settings.checkInterval) {
            this.handleSessionTimeout();
            return;
        }
        
        // إذا اقترب من انتهاء الوقت وتم تفعيل التحذيرات
        if (this.settings.showWarnings && 
            !this.state.warningShown && 
            timeSinceActivity >= (this.settings.checkInterval - this.settings.warningTime)) {
            this.showWarning();
        }
    },
    
    // معالجة انتهاء الجلسة
    handleSessionTimeout: function() {
        this.stopMonitoring();
        
        // فحص حالة الجلسة مع الخادم
        this.checkServerSession()
            .then(response => {
                if (!response.valid) {
                    this.redirectToLogin('انتهت صلاحية الجلسة');
                } else {
                    // الجلسة ما زالت صالحة، استمر في المراقبة
                    this.updateActivity();
                }
            })
            .catch(error => {
                console.warn('Session check failed:', error);
                // في حالة فشل الفحص، لا نقوم بإعادة التوجيه
                this.updateActivity();
            });
    },
    
    // عرض تحذير
    showWarning: function() {
        if (this.state.warningShown) return;
        
        this.state.warningShown = true;
        
        // إنشاء تحذير بسيط
        const warning = document.createElement('div');
        warning.id = 'session-warning';
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            max-width: 350px;
            font-size: 14px;
            line-height: 1.4;
        `;
        
        warning.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <i class="fas fa-exclamation-triangle" style="margin-left: 8px; color: #f39c12;"></i>
                <strong>تحذير الجلسة</strong>
            </div>
            <div style="margin-bottom: 15px;">
                ستنتهي جلستك قريباً بسبب عدم النشاط
            </div>
            <div style="text-align: center;">
                <button onclick="SessionMonitor.extendSession()" 
                        style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                    تمديد الجلسة
                </button>
                <button onclick="SessionMonitor.dismissWarning()" 
                        style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    إغلاق
                </button>
            </div>
        `;
        
        document.body.appendChild(warning);
        
        // إزالة التحذير تلقائياً بعد دقيقتين
        setTimeout(() => {
            this.dismissWarning();
        }, 120000);
    },
    
    // تمديد الجلسة
    extendSession: function() {
        this.updateActivity();
        this.dismissWarning();
        
        // إرسال طلب لتجديد الجلسة
        this.renewServerSession()
            .then(response => {
                if (response.success) {
                    this.showMessage('تم تمديد الجلسة بنجاح', 'success');
                }
            })
            .catch(error => {
                console.warn('Session renewal failed:', error);
            });
    },
    
    // إغلاق التحذير
    dismissWarning: function() {
        const warning = document.getElementById('session-warning');
        if (warning) {
            warning.remove();
        }
        this.state.warningShown = false;
    },
    
    // فحص الجلسة مع الخادم
    checkServerSession: async function() {
        try {
            const response = await fetch('includes/auth.php?action=check_session', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                return { valid: false };
            }
        } catch (error) {
            console.warn('Session check error:', error);
            return { valid: true }; // افتراض أن الجلسة صالحة في حالة الخطأ
        }
    },
    
    // تجديد الجلسة مع الخادم
    renewServerSession: async function() {
        try {
            const response = await fetch('includes/auth.php?action=renew_session', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                return { success: false };
            }
        } catch (error) {
            console.warn('Session renewal error:', error);
            return { success: false };
        }
    },
    
    // إعادة التوجيه لصفحة تسجيل الدخول
    redirectToLogin: function(message = '') {
        // حفظ الرسالة في التخزين المحلي
        if (message) {
            try {
                localStorage.setItem('login_message', message);
            } catch (error) {
                console.warn('LocalStorage error:', error);
            }
        }
        
        // إعادة التوجيه
        window.location.href = 'auth/login.php';
    },
    
    // عرض رسالة
    showMessage: function(text, type = 'info') {
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : '#d1ecf1'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#bee5eb'};
            color: ${type === 'success' ? '#155724' : '#0c5460'};
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            max-width: 300px;
            font-size: 14px;
        `;
        
        message.textContent = text;
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    },
    
    // تحديث الإعدادات
    updateSettings: function(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        if (this.settings.enabled && !this.state.isActive) {
            this.init();
        } else if (!this.settings.enabled && this.state.isActive) {
            this.stopMonitoring();
        }
    }
};

// تهيئة المراقب عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود جلسة نشطة
    if (document.body.dataset.userLoggedIn === 'true') {
        SessionMonitor.init();
    }
});

// تصدير للاستخدام العام
window.SessionMonitor = SessionMonitor;

console.log('Trust Plus Simple Session Monitor loaded successfully');
