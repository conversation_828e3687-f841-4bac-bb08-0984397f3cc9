/**
 * Trust Plus - Simple Session Monitor
 * مراقب الجلسة المبسط
 */

// متغيرات عامة
let sessionMonitor = {
    isActive: false,
    lastActivity: Date.now(),
    warningShown: false,
    checkTimer: null,
    options: {
        checkInterval: 60000, // فحص كل دقيقة
        warningTime: 300000, // تحذير قبل 5 دقائق
        sessionTimeout: 3600000, // انتهاء بعد ساعة
        apiEndpoint: '../dashboard/api/check_session.php',
        loginUrl: '../auth/login.php'
    }
};

// تهيئة مراقب الجلسة
function initSessionMonitor() {
    if (!shouldMonitorSession()) {
        return;
    }
    
    console.log('تم تهيئة مراقب الجلسة');
    
    sessionMonitor.isActive = true;
    sessionMonitor.lastActivity = Date.now();
    
    setupActivityListeners();
    startSessionMonitoring();
    createWarningModal();
}

// التحقق من ضرورة مراقبة الجلسة
function shouldMonitorSession() {
    return document.body.classList.contains('logged-in') || 
           document.body.hasAttribute('data-session-monitor') ||
           window.location.pathname.includes('dashboard');
}

// إعداد مستمعي الأحداث للنشاط
function setupActivityListeners() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
        document.addEventListener(event, updateActivity, true);
    });
}

// تحديث وقت آخر نشاط
function updateActivity() {
    sessionMonitor.lastActivity = Date.now();
    sessionMonitor.warningShown = false;
    hideSessionWarning();
}

// بدء مراقبة الجلسة
function startSessionMonitoring() {
    if (sessionMonitor.checkTimer) {
        clearInterval(sessionMonitor.checkTimer);
    }
    
    sessionMonitor.checkTimer = setInterval(checkSessionStatus, sessionMonitor.options.checkInterval);
}

// فحص حالة الجلسة
async function checkSessionStatus() {
    try {
        const response = await fetch(sessionMonitor.options.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'check_session'
            })
        });
        
        if (!response.ok) {
            throw new Error('فشل في الاتصال بالخادم');
        }
        
        const result = await response.json();
        
        if (!result.success) {
            handleSessionExpired(result.reason || 'session_expired');
            return;
        }
        
        // التحقق من الوقت المتبقي للجلسة
        const timeInactive = Date.now() - sessionMonitor.lastActivity;
        const timeUntilExpiry = sessionMonitor.options.sessionTimeout - timeInactive;
        
        if (timeUntilExpiry <= sessionMonitor.options.warningTime && !sessionMonitor.warningShown) {
            showSessionWarning(Math.floor(timeUntilExpiry / 1000));
        }
        
    } catch (error) {
        console.warn('خطأ في فحص الجلسة:', error.message);
        // لا نقوم بأي إجراء في حالة فشل الاتصال
    }
}

// عرض تحذير انتهاء الجلسة
function showSessionWarning(secondsLeft) {
    sessionMonitor.warningShown = true;
    
    const modal = document.getElementById('sessionWarningModal');
    const countdown = document.getElementById('sessionCountdown');
    
    if (!modal || !countdown) {
        console.warn('لم يتم العثور على عناصر نافذة التحذير');
        return;
    }
    
    countdown.textContent = formatTime(secondsLeft);
    
    // إظهار النافذة
    modal.style.display = 'block';
    modal.classList.add('show');
    document.body.classList.add('modal-open');
    
    // إضافة backdrop
    let backdrop = document.querySelector('.modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
    }
    
    // تحديث العد التنازلي
    const countdownInterval = setInterval(() => {
        secondsLeft--;
        countdown.textContent = formatTime(secondsLeft);
        
        if (secondsLeft <= 0) {
            clearInterval(countdownInterval);
            handleSessionExpired('session_timeout');
        }
    }, 1000);
    
    // إعداد أزرار النافذة
    const extendBtn = document.getElementById('extendSessionBtn');
    const logoutBtn = document.getElementById('logoutSessionBtn');
    
    if (extendBtn) {
        extendBtn.onclick = () => {
            clearInterval(countdownInterval);
            extendSession();
            hideSessionWarning();
        };
    }
    
    if (logoutBtn) {
        logoutBtn.onclick = () => {
            clearInterval(countdownInterval);
            logout('manual');
        };
    }
}

// إخفاء تحذير الجلسة
function hideSessionWarning() {
    const modal = document.getElementById('sessionWarningModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
        
        // إزالة backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
}

// تمديد الجلسة
async function extendSession() {
    try {
        const response = await fetch(sessionMonitor.options.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'extend_session'
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            updateActivity();
            showNotification('تم تمديد الجلسة بنجاح', 'success');
        } else {
            handleSessionExpired('extend_failed');
        }
        
    } catch (error) {
        console.error('خطأ في تمديد الجلسة:', error);
        handleSessionExpired('extend_error');
    }
}

// معالجة انتهاء الجلسة
function handleSessionExpired(reason) {
    if (sessionMonitor.checkTimer) {
        clearInterval(sessionMonitor.checkTimer);
        sessionMonitor.checkTimer = null;
    }
    
    const messages = {
        'session_timeout': 'انتهت صلاحية الجلسة بسبب عدم النشاط',
        'max_session_exceeded': 'تم تجاوز الحد الأقصى لمدة الجلسة',
        'session_expired': 'انتهت صلاحية الجلسة',
        'extend_failed': 'فشل في تمديد الجلسة',
        'extend_error': 'خطأ في تمديد الجلسة'
    };
    
    const message = messages[reason] || messages['session_expired'];
    
    showNotification(message, 'warning');
    
    // إعادة التوجيه بعد 3 ثوانٍ
    setTimeout(() => {
        logout(reason);
    }, 3000);
}

// تسجيل الخروج
function logout(reason) {
    const url = `${sessionMonitor.options.loginUrl}?reason=${encodeURIComponent(reason)}`;
    window.location.href = url;
}

// تنسيق الوقت
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    const icons = {
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'info': 'fas fa-info-circle'
    };
    
    notification.innerHTML = `
        <i class="${icons[type] || icons['info']} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// إنشاء نافذة تحذير انتهاء الجلسة
function createWarningModal() {
    if (document.getElementById('sessionWarningModal')) {
        return; // النافذة موجودة بالفعل
    }
    
    const modalHTML = `
        <div class="modal fade" id="sessionWarningModal" tabindex="-1" style="z-index: 9999;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-clock me-2"></i>
                            تحذير انتهاء الجلسة
                        </h5>
                    </div>
                    <div class="modal-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-hourglass-half fa-3x text-warning mb-3"></i>
                        </div>
                        <p class="mb-3">ستنتهي جلستك خلال:</p>
                        <h3 class="text-danger mb-3">
                            <span id="sessionCountdown">5:00</span>
                        </h3>
                        <p class="text-muted">هل تريد تمديد الجلسة أم تسجيل الخروج؟</p>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-success" id="extendSessionBtn">
                            <i class="fas fa-clock me-2"></i>
                            تمديد الجلسة
                        </button>
                        <button type="button" class="btn btn-danger" id="logoutSessionBtn">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(initSessionMonitor, 1000);
});

// تصدير الدوال للاستخدام العام
window.sessionMonitor = sessionMonitor;
window.updateActivity = updateActivity;
window.extendSession = extendSession;
window.logout = logout;
