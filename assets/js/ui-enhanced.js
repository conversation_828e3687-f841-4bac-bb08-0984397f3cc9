/**
 * Trust Plus - Enhanced UI JavaScript
 * ملف JavaScript محسن للواجهة
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('Trust Plus Enhanced UI Initialized');
    
    // تهيئة المكونات
    initializeEnhancedComponents();
    
    // تهيئة الأحداث
    initializeEnhancedEvents();
    
    // تحديث الوقت
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // تهيئة الشريط الجانبي
    initializeSidebarBehavior();
});

// تهيئة المكونات المحسنة
function initializeEnhancedComponents() {
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة Bootstrap components
    initializeBootstrapComponents();
    
    // تهيئة الرسوم البيانية البديلة
    initializePlaceholderCharts();
}

// تهيئة Bootstrap components
function initializeBootstrapComponents() {
    // تهيئة Tooltips
    try {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            }
        });
    } catch (error) {
        console.warn('Bootstrap Tooltips not available:', error);
    }
    
    // تهيئة Popovers
    try {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
                return new bootstrap.Popover(popoverTriggerEl);
            }
        });
    } catch (error) {
        console.warn('Bootstrap Popovers not available:', error);
    }
    
    // تهيئة Dropdowns
    try {
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.map(function (dropdownToggleEl) {
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            }
        });
    } catch (error) {
        console.warn('Bootstrap Dropdowns not available:', error);
    }
}

// تهيئة سلوك الشريط الجانبي
function initializeSidebarBehavior() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const backdrop = document.querySelector('.sidebar-backdrop');
    
    // إخفاء زر التبديل في الشاشات الكبيرة
    if (window.innerWidth >= 992) {
        if (sidebarToggle) {
            sidebarToggle.style.display = 'none';
        }
    }
    
    // مراقبة تغيير حجم النافذة
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            // الشاشات الكبيرة
            if (sidebar) {
                sidebar.classList.remove('show');
                sidebar.style.transform = '';
            }
            if (backdrop) {
                backdrop.classList.remove('show');
            }
            if (sidebarToggle) {
                sidebarToggle.style.display = 'none';
            }
        } else {
            // الشاشات الصغيرة
            if (sidebarToggle) {
                sidebarToggle.style.display = 'flex';
            }
        }
    });
}

// تهيئة الأحداث المحسنة
function initializeEnhancedEvents() {
    // أحداث الأزرار
    initializeButtons();
    
    // أحداث النماذج
    initializeFormEvents();
    
    // أحداث الشريط الجانبي
    initializeSidebar();
    
    // أحداث لوحة المفاتيح
    initializeKeyboardEvents();
    
    // أحداث التمرير
    initializeScrollEvents();
}

// تهيئة أحداث لوحة المفاتيح
function initializeKeyboardEvents() {
    document.addEventListener('keydown', function(e) {
        // إغلاق الشريط الجانبي بالضغط على Escape
        if (e.key === 'Escape') {
            closeSidebar();
        }
        
        // تبديل الشريط الجانبي بالضغط على Ctrl+B
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
    });
}

// تهيئة أحداث التمرير
function initializeScrollEvents() {
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // إخفاء/إظهار شريط التنقل عند التمرير
        if (navbar) {
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // التمرير لأسفل
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // التمرير لأعلى
                navbar.style.transform = 'translateY(0)';
            }
        }
        
        lastScrollTop = scrollTop;
    });
}

// تهيئة الجداول
function initializeTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        addTableFeatures(table);
    });
}

// تهيئة النماذج
function initializeForms() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// تهيئة التنبيهات
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert[data-auto-dismiss]');
    alerts.forEach(alert => {
        const delay = parseInt(alert.dataset.autoDismiss) || 5000;
        setTimeout(() => {
            alert.classList.add('fade');
            setTimeout(() => alert.remove(), 300);
        }, delay);
    });
}

// تهيئة الرسوم البيانية البديلة
function initializePlaceholderCharts() {
    console.log('Chart.js Placeholder loaded');
    
    // إنشاء رسوم بيانية بديلة بسيطة
    const chartContainers = document.querySelectorAll('.chart-canvas');
    chartContainers.forEach(container => {
        if (!container.querySelector('canvas')) {
            createPlaceholderChart(container);
        }
    });
}

// إنشاء رسم بياني بديل
function createPlaceholderChart(container) {
    const canvas = document.createElement('canvas');
    canvas.width = container.offsetWidth || 400;
    canvas.height = container.offsetHeight || 300;
    
    const ctx = canvas.getContext('2d');
    
    // رسم خلفية
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // رسم خطوط الشبكة
    ctx.strokeStyle = '#e9ecef';
    ctx.lineWidth = 1;
    
    for (let i = 0; i <= 10; i++) {
        const y = (canvas.height / 10) * i;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
    
    for (let i = 0; i <= 10; i++) {
        const x = (canvas.width / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }
    
    // رسم خط بياني بسيط
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const points = [
        {x: canvas.width * 0.1, y: canvas.height * 0.8},
        {x: canvas.width * 0.3, y: canvas.height * 0.6},
        {x: canvas.width * 0.5, y: canvas.height * 0.4},
        {x: canvas.width * 0.7, y: canvas.height * 0.3},
        {x: canvas.width * 0.9, y: canvas.height * 0.2}
    ];
    
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();
    
    // إضافة نقاط
    ctx.fillStyle = '#667eea';
    points.forEach(point => {
        ctx.beginPath();
        ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
        ctx.fill();
    });
    
    // إضافة نص
    ctx.fillStyle = '#6c757d';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('رسم بياني تجريبي', canvas.width / 2, canvas.height / 2);
    
    container.appendChild(canvas);
}

// تهيئة الأزرار
function initializeButtons() {
    const loadingButtons = document.querySelectorAll('[data-loading]');
    loadingButtons.forEach(button => {
        button.addEventListener('click', function() {
            showButtonLoading(this);
        });
    });
}

// تهيئة أحداث النماذج
function initializeFormEvents() {
    const searchInputs = document.querySelectorAll('[data-live-search]');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            performLiveSearch(this);
        });
    });
}

// تهيئة الشريط الجانبي
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const backdrop = document.querySelector('.sidebar-backdrop');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleSidebar();
        });
    }
    
    if (backdrop) {
        backdrop.addEventListener('click', function() {
            closeSidebar();
        });
    }
    
    // إغلاق الشريط الجانبي عند النقر خارجه
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 992) {
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            
            if (sidebar && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    closeSidebar();
                }
            }
        }
    });
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebar) {
        const isShowing = sidebar.classList.contains('show');
        
        if (isShowing) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }
}

// فتح الشريط الجانبي
function openSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const backdrop = document.querySelector('.sidebar-backdrop');
    
    if (sidebar) {
        sidebar.classList.add('show');
        if (backdrop) {
            backdrop.classList.add('show');
        }
        
        // منع التمرير في الخلفية للشاشات الصغيرة
        if (window.innerWidth < 992) {
            document.body.style.overflow = 'hidden';
        }
    }
}

// إغلاق الشريط الجانبي
function closeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const backdrop = document.querySelector('.sidebar-backdrop');
    
    if (sidebar) {
        sidebar.classList.remove('show');
        if (backdrop) {
            backdrop.classList.remove('show');
        }
        
        // إعادة تفعيل التمرير
        document.body.style.overflow = '';
    }
}

// إضافة ميزات للجداول
function addTableFeatures(table) {
    const headers = table.querySelectorAll('th[data-sortable]');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(table, this);
        });
    });
}

// فرز الجدول
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // إزالة فئات الفرز من جميع الرؤوس
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // إضافة فئة الفرز للرأس الحالي
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // فرز الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        if (isAscending) {
            return aValue.localeCompare(bValue, 'ar', { numeric: true });
        } else {
            return bValue.localeCompare(aValue, 'ar', { numeric: true });
        }
    });
    
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
}

// عرض حالة التحميل للزر
function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    button.disabled = true;
    
    // إعادة تعيين الزر بعد 3 ثوانٍ
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 3000);
}

// البحث المباشر
function performLiveSearch(input) {
    const searchTerm = input.value.toLowerCase();
    const targetSelector = input.dataset.liveSearch;
    const targets = document.querySelectorAll(targetSelector);
    
    targets.forEach(target => {
        const text = target.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            target.style.display = '';
        } else {
            target.style.display = 'none';
        }
    });
}

// تحديث التاريخ والوقت
function updateDateTime() {
    const dateTimeElement = document.querySelector('#current-datetime');
    if (dateTimeElement) {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Jerusalem'
        };
        dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// تحسين TrustPlus object
if (typeof window.TrustPlus === 'undefined') {
    window.TrustPlus = {};
}

// إضافة دوال UI محسنة
window.TrustPlus.ui = {
    ...window.TrustPlus.ui,
    toggleSidebar: toggleSidebar,
    openSidebar: openSidebar,
    closeSidebar: closeSidebar,
    showAlert: function(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
};

console.log('Enhanced UI JavaScript loaded successfully');
