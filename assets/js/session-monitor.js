/**
 * Trust Plus - Session Monitor
 * مراقب الجلسة التلقائي
 */

class SessionMonitor {
    constructor(options = {}) {
        this.options = {
            checkInterval: options.checkInterval || 60000, // فحص كل دقيقة
            warningTime: options.warningTime || 300000, // تحذير قبل 5 دقائق
            sessionTimeout: options.sessionTimeout || 3600000, // انتهاء بعد ساعة
            apiEndpoint: options.apiEndpoint || 'api/check_session.php',
            loginUrl: options.loginUrl || 'auth/login.php',
            ...options
        };
        
        this.isActive = true;
        this.lastActivity = Date.now();
        this.warningShown = false;
        this.checkTimer = null;
        this.warningTimer = null;
        
        this.init();
    }
    
    init() {
        this.setupActivityListeners();
        this.startMonitoring();
        this.setupWarningModal();
    }
    
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, true);
        });
    }
    
    updateActivity() {
        this.lastActivity = Date.now();
        this.warningShown = false;

        // إخفاء تحذير انتهاء الجلسة إذا كان ظاهراً
        try {
            this.hideWarning();
        } catch (error) {
            // تجاهل الأخطاء في إخفاء التحذير
            console.debug('تم تجاهل خطأ في إخفاء التحذير:', error);
        }
    }
    
    startMonitoring() {
        this.checkTimer = setInterval(() => {
            this.checkSession();
        }, this.options.checkInterval);
    }
    
    stopMonitoring() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        
        if (this.warningTimer) {
            clearTimeout(this.warningTimer);
            this.warningTimer = null;
        }
    }
    
    async checkSession() {
        try {
            const response = await fetch(this.options.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check_session'
                })
            });
            
            const result = await response.json();
            
            if (!result.success) {
                this.handleSessionExpired(result.reason || 'session_expired');
                return;
            }
            
            // التحقق من الوقت المتبقي للجلسة
            const timeInactive = Date.now() - this.lastActivity;
            const timeUntilExpiry = this.options.sessionTimeout - timeInactive;
            
            if (timeUntilExpiry <= this.options.warningTime && !this.warningShown) {
                this.showWarning(Math.floor(timeUntilExpiry / 1000));
            }
            
        } catch (error) {
            console.warn('خطأ في فحص الجلسة:', error);
            // في حالة فشل الاتصال، لا نقوم بأي إجراء
            // قد يكون بسبب مشكلة مؤقتة في الشبكة
        }
    }
    
    showWarning(secondsLeft) {
        this.warningShown = true;
        
        const modal = document.getElementById('sessionWarningModal');
        const countdown = document.getElementById('sessionCountdown');
        
        if (modal && countdown) {
            countdown.textContent = this.formatTime(secondsLeft);

            // إظهار النافذة المنبثقة
            try {
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    // إظهار يدوي إذا لم يكن Bootstrap متوفراً
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');

                    // إضافة backdrop
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    document.body.appendChild(backdrop);
                }
            } catch (error) {
                console.warn('خطأ في إظهار نافذة التحذير:', error);
                // إظهار يدوي كبديل
                modal.style.display = 'block';
            }
            
            // تحديث العد التنازلي
            const countdownInterval = setInterval(() => {
                secondsLeft--;
                countdown.textContent = this.formatTime(secondsLeft);
                
                if (secondsLeft <= 0) {
                    clearInterval(countdownInterval);
                    this.handleSessionExpired('session_timeout');
                }
            }, 1000);
            
            // إعداد أزرار النافذة المنبثقة
            const extendBtn = document.getElementById('extendSessionBtn');
            const logoutBtn = document.getElementById('logoutSessionBtn');
            
            if (extendBtn) {
                extendBtn.onclick = () => {
                    clearInterval(countdownInterval);
                    this.extendSession();
                    this.hideWarning();
                };
            }

            if (logoutBtn) {
                logoutBtn.onclick = () => {
                    clearInterval(countdownInterval);
                    this.logout('manual');
                };
            }
        }
    }
    
    hideWarning() {
        const modal = document.getElementById('sessionWarningModal');
        if (modal) {
            try {
                // التحقق من وجود Bootstrap
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                } else {
                    // إخفاء يدوي إذا لم يكن Bootstrap متوفراً
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');

                    // إزالة backdrop إذا كان موجوداً
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }
            } catch (error) {
                console.warn('خطأ في إخفاء نافذة التحذير:', error);
                // إخفاء يدوي كبديل
                modal.style.display = 'none';
            }
        }
    }
    
    async extendSession() {
        try {
            const response = await fetch(this.options.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'extend_session'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateActivity();
                this.showNotification('تم تمديد الجلسة بنجاح', 'success');
            } else {
                this.handleSessionExpired('extend_failed');
            }
            
        } catch (error) {
            console.error('خطأ في تمديد الجلسة:', error);
            this.handleSessionExpired('extend_error');
        }
    }
    
    handleSessionExpired(reason) {
        this.stopMonitoring();
        
        const messages = {
            'session_timeout': 'انتهت صلاحية الجلسة بسبب عدم النشاط',
            'max_session_exceeded': 'تم تجاوز الحد الأقصى لمدة الجلسة',
            'session_expired': 'انتهت صلاحية الجلسة',
            'extend_failed': 'فشل في تمديد الجلسة',
            'extend_error': 'خطأ في تمديد الجلسة'
        };
        
        const message = messages[reason] || messages['session_expired'];
        
        this.showNotification(message, 'warning');
        
        // إعادة التوجيه بعد 3 ثوانٍ
        setTimeout(() => {
            this.logout(reason);
        }, 3000);
    }
    
    logout(reason) {
        const url = `${this.options.loginUrl}?reason=${encodeURIComponent(reason)}`;
        window.location.href = url;
    }
    
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    showNotification(message, type = 'info') {
        // إنشاء إشعار
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        const icons = {
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
            'info': 'fas fa-info-circle'
        };
        
        notification.innerHTML = `
            <i class="${icons[type] || icons['info']} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    setupWarningModal() {
        // إنشاء نافذة تحذير انتهاء الجلسة إذا لم تكن موجودة
        if (!document.getElementById('sessionWarningModal')) {
            const modalHTML = `
                <div class="modal fade" id="sessionWarningModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-clock me-2"></i>
                                    تحذير انتهاء الجلسة
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-hourglass-half fa-3x text-warning mb-3"></i>
                                </div>
                                <p class="mb-3">ستنتهي جلستك خلال:</p>
                                <h3 class="text-danger mb-3">
                                    <span id="sessionCountdown">5:00</span>
                                </h3>
                                <p class="text-muted">هل تريد تمديد الجلسة أم تسجيل الخروج؟</p>
                            </div>
                            <div class="modal-footer justify-content-center">
                                <button type="button" class="btn btn-success" id="extendSessionBtn">
                                    <i class="fas fa-clock me-2"></i>
                                    تمديد الجلسة
                                </button>
                                <button type="button" class="btn btn-danger" id="logoutSessionBtn">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }
    }
}

// تهيئة مراقب الجلسة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود جلسة نشطة
    if (document.body.classList.contains('logged-in') || 
        window.location.pathname.includes('dashboard') ||
        document.querySelector('[data-session-monitor="true"]')) {
        
        window.sessionMonitor = new SessionMonitor({
            checkInterval: 60000, // فحص كل دقيقة
            warningTime: 300000, // تحذير قبل 5 دقائق
            sessionTimeout: 3600000, // انتهاء بعد ساعة
            apiEndpoint: '../api/check_session.php',
            loginUrl: '../auth/login.php'
        });
    }
});
