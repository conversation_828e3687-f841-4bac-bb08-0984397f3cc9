/**
 * Trust Plus - Dashboard JavaScript
 * ملف JavaScript خاص بلوحة التحكم
 */

const Dashboard = {
    // بيانات لوحة التحكم
    data: {
        stats: {},
        charts: {},
        activities: []
    },
    
    // الرسوم البيانية
    charts: {},
    
    // المؤقتات
    timers: {},
    
    /**
     * تهيئة لوحة التحكم
     */
    init: function() {
        this.loadStats();
        this.initCharts();
        this.loadRecentActivities();
        this.startAutoRefresh();
        this.bindEvents();
        
        console.log('Dashboard initialized');
    },
    
    /**
     * تحميل الإحصائيات
     */
    loadStats: function() {
        TrustPlus.ui.showLoading('.quick-stats');
        
        TrustPlus.api.get('api/dashboard_stats.php')
            .then(response => {
                if (response.success) {
                    this.updateStats(response.data);
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
            })
            .finally(() => {
                TrustPlus.ui.hideLoading('.quick-stats');
            });
    },
    
    /**
     * تحديث الإحصائيات
     */
    updateStats: function(stats) {
        this.data.stats = stats;
        
        // تحديث بطاقات الإحصائيات
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                const value = stats[key].value || stats[key];
                const change = stats[key].change || 0;
                
                // تحديث القيمة مع تأثير العداد
                TrustPlus.ui.animateCounter(element.querySelector('.stat-value'), value);
                
                // تحديث نسبة التغيير
                const changeElement = element.querySelector('.stat-change');
                if (changeElement && change !== undefined) {
                    changeElement.textContent = `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
                    changeElement.className = `stat-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            }
        });
    },
    
    /**
     * تهيئة الرسوم البيانية
     */
    initCharts: function() {
        this.initRevenueChart();
        this.initTransactionChart();
        this.initCurrencyChart();
    },
    
    /**
     * رسم بياني للإيرادات
     */
    initRevenueChart: function() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return TrustPlus.utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * رسم بياني للمعاملات
     */
    initTransactionChart: function() {
        const ctx = document.getElementById('transactionChart');
        if (!ctx) return;
        
        this.charts.transaction = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['صرافة', 'تحويلات', 'أخرى'],
                datasets: [{
                    data: [60, 30, 10],
                    backgroundColor: ['#667eea', '#764ba2', '#f093fb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    },
    
    /**
     * رسم بياني للعملات
     */
    initCurrencyChart: function() {
        const ctx = document.getElementById('currencyChart');
        if (!ctx) return;
        
        this.charts.currency = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['USD', 'EUR', 'GBP', 'SAR', 'AED'],
                datasets: [{
                    label: 'حجم التداول',
                    data: [45000, 32000, 28000, 25000, 18000],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(240, 147, 251, 0.8)',
                        'rgba(86, 171, 47, 0.8)',
                        'rgba(255, 193, 7, 0.8)'
                    ],
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return TrustPlus.utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * تحميل الأنشطة الأخيرة
     */
    loadRecentActivities: function() {
        TrustPlus.api.get('api/recent_activities.php')
            .then(response => {
                if (response.success) {
                    this.updateActivities(response.data);
                }
            })
            .catch(error => {
                console.error('Error loading activities:', error);
            });
    },
    
    /**
     * تحديث الأنشطة
     */
    updateActivities: function(activities) {
        this.data.activities = activities;
        
        const container = document.querySelector('.recent-activities .activities-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = 'activity-item';
            item.innerHTML = `
                <div class="activity-icon bg-${this.getActivityColor(activity.type)}">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${TrustPlus.utils.timeAgo(activity.created_at)}</div>
                </div>
            `;
            container.appendChild(item);
        });
    },
    
    /**
     * الحصول على لون النشاط
     */
    getActivityColor: function(type) {
        const colors = {
            'exchange': 'primary',
            'transfer': 'success',
            'customer': 'info',
            'user': 'warning',
            'system': 'secondary'
        };
        return colors[type] || 'secondary';
    },
    
    /**
     * الحصول على أيقونة النشاط
     */
    getActivityIcon: function(type) {
        const icons = {
            'exchange': 'exchange-alt',
            'transfer': 'paper-plane',
            'customer': 'user',
            'user': 'user-cog',
            'system': 'cog'
        };
        return icons[type] || 'circle';
    },
    
    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh: function() {
        // تحديث الإحصائيات كل 5 دقائق
        this.timers.stats = setInterval(() => {
            this.loadStats();
        }, 5 * 60 * 1000);
        
        // تحديث الأنشطة كل دقيقة
        this.timers.activities = setInterval(() => {
            this.loadRecentActivities();
        }, 60 * 1000);
    },
    
    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh: function() {
        Object.values(this.timers).forEach(timer => {
            clearInterval(timer);
        });
        this.timers = {};
    },
    
    /**
     * ربط الأحداث
     */
    bindEvents: function() {
        // زر التحديث اليدوي
        const refreshBtn = document.querySelector('[data-action="refresh"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refresh();
            });
        }
        
        // تبديل فترة الرسم البياني
        const periodButtons = document.querySelectorAll('[data-period]');
        periodButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const period = e.target.dataset.period;
                this.changePeriod(period);
            });
        });
        
        // تصدير البيانات
        const exportBtn = document.querySelector('[data-action="export"]');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportData();
            });
        }
    },
    
    /**
     * تحديث البيانات
     */
    refresh: function() {
        TrustPlus.utils.showToast('جاري تحديث البيانات...', 'info');
        
        Promise.all([
            this.loadStats(),
            this.loadRecentActivities()
        ]).then(() => {
            TrustPlus.utils.showToast('تم تحديث البيانات بنجاح', 'success');
        }).catch(() => {
            TrustPlus.utils.showToast('خطأ في تحديث البيانات', 'danger');
        });
    },
    
    /**
     * تغيير فترة الرسم البياني
     */
    changePeriod: function(period) {
        // تحديث الرسوم البيانية حسب الفترة المختارة
        console.log('Changing period to:', period);
        
        // تحديث البيانات من الخادم
        TrustPlus.api.get('api/chart_data.php', { period: period })
            .then(response => {
                if (response.success) {
                    this.updateChartData(response.data);
                }
            });
    },
    
    /**
     * تحديث بيانات الرسوم البيانية
     */
    updateChartData: function(data) {
        if (this.charts.revenue && data.revenue) {
            this.charts.revenue.data.labels = data.revenue.labels;
            this.charts.revenue.data.datasets[0].data = data.revenue.data;
            this.charts.revenue.update();
        }
        
        if (this.charts.transaction && data.transaction) {
            this.charts.transaction.data.datasets[0].data = data.transaction.data;
            this.charts.transaction.update();
        }
        
        if (this.charts.currency && data.currency) {
            this.charts.currency.data.labels = data.currency.labels;
            this.charts.currency.data.datasets[0].data = data.currency.data;
            this.charts.currency.update();
        }
    },
    
    /**
     * تصدير البيانات
     */
    exportData: function() {
        const data = [
            { metric: 'إجمالي الإيرادات', value: this.data.stats.total_revenue || 0 },
            { metric: 'عدد المعاملات', value: this.data.stats.total_transactions || 0 },
            { metric: 'عدد العملاء', value: this.data.stats.total_customers || 0 },
            { metric: 'متوسط المعاملة', value: this.data.stats.avg_transaction || 0 }
        ];
        
        TrustPlus.utils.exportToCSV(data, `dashboard_stats_${TrustPlus.utils.formatDate(new Date())}.csv`);
    },
    
    /**
     * تنظيف الموارد
     */
    destroy: function() {
        this.stopAutoRefresh();
        
        // تدمير الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
        this.data = { stats: {}, charts: {}, activities: [] };
    }
};

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.dashboard-container')) {
        Dashboard.init();
    }
});

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (typeof Dashboard !== 'undefined') {
        Dashboard.destroy();
    }
});

// تصدير للاستخدام العام
window.Dashboard = Dashboard;
